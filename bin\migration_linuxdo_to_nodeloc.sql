-- Migration script to rename linux_do_id column to nodeloc_id
-- This script supports MySQL, PostgreSQL, and SQLite

-- For MySQL
-- ALTER TABLE users CHANGE COLUMN linux_do_id nodeloc_id VARCHAR(255);

-- For PostgreSQL  
-- ALTER TABLE users RENAME COLUMN linux_do_id TO nodeloc_id;

-- For SQLite (requires multiple steps due to SQLite limitations)
-- Step 1: Add new column
-- ALTER TABLE users ADD COLUMN nodeloc_id VARCHAR(255);

-- Step 2: Copy data from old column to new column
-- UPDATE users SET nodeloc_id = linux_do_id WHERE linux_do_id IS NOT NULL;

-- Step 3: Create index on new column (if needed)
-- CREATE INDEX idx_users_nodeloc_id ON users(nodeloc_id);

-- Step 4: Drop old column (SQLite doesn't support DROP COLUMN directly, so this would require table recreation)
-- For SQLite, you would need to:
-- 1. Create a new table with the correct schema
-- 2. Copy all data to the new table
-- 3. Drop the old table
-- 4. Rename the new table

-- Universal approach for all databases:
-- 1. Add new column
ALTER TABLE users ADD COLUMN nodeloc_id VARCHAR(255);

-- 2. Copy data
UPDATE users SET nodeloc_id = linux_do_id WHERE linux_do_id IS NOT NULL AND linux_do_id != '';

-- 3. Create index on new column
CREATE INDEX idx_users_nodeloc_id ON users(nodeloc_id);

-- Note: Dropping the old column should be done manually after verifying the migration
-- because SQLite doesn't support DROP COLUMN and it's safer to keep the old data
-- until you're sure the migration worked correctly.

-- To complete the migration (run these commands manually after verification):
-- For MySQL:
-- ALTER TABLE users DROP COLUMN linux_do_id;

-- For PostgreSQL:
-- ALTER TABLE users DROP COLUMN linux_do_id;

-- For SQLite:
-- You would need to recreate the table without the old column
