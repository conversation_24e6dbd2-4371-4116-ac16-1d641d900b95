import React, { useContext, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { API, showError, showSuccess, updateAPI, setUserData } from '../../helpers';
import { UserContext } from '../../context/User';
import Loading from '../common/Loading';

const OAuth2Callback = (props) => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const [, userDispatch] = useContext(UserContext);
  const navigate = useNavigate();

  // 最大重试次数
  const MAX_RETRIES = 3;

  const sendCode = async (code, state, retry = 0) => {
    try {
      const apiUrl = `/api/oauth/${props.type}?code=${code}&state=${state}`;
      console.log('OAuth2Callback - Calling API:', apiUrl);
      console.log('OAuth2Callback - Props type:', props.type);
      console.log('OAuth2Callback - Code:', code);
      console.log('OAuth2Callback - State:', state);

      const { data: resData } = await API.get(apiUrl);

      console.log('OAuth2Callback - API Response:', resData);

      const { success, message, data } = resData;

      if (!success) {
        console.error('OAuth2Callback - API returned success=false:', message);
        throw new Error(message || 'OAuth2 callback error');
      }

      if (message === 'bind') {
        showSuccess(t('绑定成功！'));
        navigate('/console/personal');
      } else {
        userDispatch({ type: 'login', payload: data });
        localStorage.setItem('user', JSON.stringify(data));
        setUserData(data);
        updateAPI();
        showSuccess(t('登录成功！'));
        navigate('/console/token');
      }
    } catch (error) {
      console.error('OAuth2Callback - API Error:', error);
      console.error('OAuth2Callback - Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });

      if (retry < MAX_RETRIES) {
        // 递增的退避等待
        await new Promise((resolve) => setTimeout(resolve, (retry + 1) * 2000));
        return sendCode(code, state, retry + 1);
      }

      // 重试次数耗尽，提示错误并返回设置页面
      const errorMsg = error.response?.data?.message || error.message || t('授权失败');
      showError(`OAuth错误: ${errorMsg}`);
      navigate('/console/personal');
    }
  };

  useEffect(() => {
    console.log('OAuth2Callback - Component mounted');
    console.log('OAuth2Callback - Props:', props);

    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    // 添加调试信息
    console.log('OAuth2Callback - URL params:', {
      code,
      state,
      error,
      errorDescription,
      fullURL: window.location.href,
      search: window.location.search,
      hash: window.location.hash,
      searchParams: Object.fromEntries(searchParams.entries()),
      type: props.type
    });

    // 检查是否有错误参数
    if (error) {
      showError(t('OAuth授权失败') + ': ' + (errorDescription || error));
      navigate('/console/personal');
      return;
    }

    // 参数缺失直接返回
    if (!code) {
      showError(t('未获取到授权码') + ' - URL: ' + window.location.href);
      navigate('/console/personal');
      return;
    }

    sendCode(code, state);
  }, []);

  return <Loading />;
};

export default OAuth2Callback;
