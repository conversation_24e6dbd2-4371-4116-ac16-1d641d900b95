package controller

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"one-api/common"
	"one-api/model"
	"strconv"
	"strings"
	"time"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type NodelocUser struct {
	Id         int    `json:"id"`
	Username   string `json:"username"`
	Name       string `json:"name"`
	Active     bool   `json:"active"`
	TrustLevel int    `json:"trust_level"`
	Silenced   bool   `json:"silenced"`
}

func NodelocBind(c *gin.Context) {
	if !common.NodelocOAuthEnabled {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "管理员未开启通过 Nodeloc 登录以及注册",
		})
		return
	}

	code := c.Query("code")
	nodelocUser, err := getNodelocUserInfoByCode(code, c)
	if err != nil {
		common.ApiError(c, err)
		return
	}

	user := model.User{
		NodelocId: strconv.Itoa(nodelocUser.Id),
	}

	if model.IsNodelocIdAlreadyTaken(user.NodelocId) {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "该 Nodeloc 账户已被绑定",
		})
		return
	}

	session := sessions.Default(c)
	id := session.Get("id")
	user.Id = id.(int)

	err = user.FillUserById()
	if err != nil {
		common.ApiError(c, err)
		return
	}

	user.NodelocId = strconv.Itoa(nodelocUser.Id)
	err = user.Update(false)
	if err != nil {
		common.ApiError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "bind",
	})
}

func getNodelocUserInfoByCode(code string, c *gin.Context) (*NodelocUser, error) {
	if code == "" {
		return nil, errors.New("invalid code")
	}

	// Get access token using Basic auth
	tokenEndpoint := "https://conn.nodeloc.cc/oauth2/token"
	credentials := common.NodelocClientId + ":" + common.NodelocClientSecret
	basicAuth := "Basic " + base64.StdEncoding.EncodeToString([]byte(credentials))

	// Get redirect URI - must match exactly what frontend used
	// Since frontend uses window.location.origin + '/oauth/nodeloc'
	// and you're running on https://api.223220.xyz:11443
	// we need to ensure the redirect_uri matches exactly

	// Try to get the origin from headers first
	origin := c.GetHeader("Origin")
	if origin == "" {
		// Fallback: construct from request
		scheme := "https"
		if c.Request.TLS == nil && c.GetHeader("X-Forwarded-Proto") != "https" {
			scheme = "http"
		}
		// Make sure to include port if it exists
		host := c.Request.Host
		origin = fmt.Sprintf("%s://%s", scheme, host)
	}

	// Temporarily hardcode for testing - replace with your exact URL
	redirectURI := "https://api.223220.xyz:11443/oauth/nodeloc"

	// Add debug logging
	fmt.Printf("Debug - Origin: %s\n", origin)
	fmt.Printf("Debug - Redirect URI (hardcoded): %s\n", redirectURI)
	fmt.Printf("Debug - Request Host: %s\n", c.Request.Host)
	fmt.Printf("Debug - Client ID: %s\n", common.NodelocClientId)
	fmt.Printf("Debug - Client Secret length: %d\n", len(common.NodelocClientSecret))

	data := url.Values{}
	data.Set("grant_type", "authorization_code")
	data.Set("code", code)
	data.Set("redirect_uri", redirectURI)

	req, err := http.NewRequest("POST", tokenEndpoint, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", basicAuth)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Accept", "application/json")

	client := http.Client{Timeout: 5 * time.Second}
	res, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Nodeloc server: %v", err)
	}
	defer res.Body.Close()

	// Read response body for debugging
	bodyBytes, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	// Log the response for debugging
	fmt.Printf("Debug - Token response status: %d\n", res.StatusCode)
	fmt.Printf("Debug - Token response body: %s\n", string(bodyBytes))

	var tokenRes struct {
		AccessToken      string `json:"access_token"`
		Message          string `json:"message"`
		Error            string `json:"error"`
		ErrorDescription string `json:"error_description"`
	}

	if err := json.Unmarshal(bodyBytes, &tokenRes); err != nil {
		return nil, fmt.Errorf("failed to parse token response: %v, body: %s", err, string(bodyBytes))
	}

	if tokenRes.AccessToken == "" {
		if tokenRes.Error != "" {
			// Return more detailed error for frontend
			return nil, fmt.Errorf("Nodeloc OAuth错误: %s - %s (检查Client ID/Secret配置)", tokenRes.Error, tokenRes.ErrorDescription)
		}
		return nil, fmt.Errorf("获取访问令牌失败: %s (响应: %s)", tokenRes.Message, string(bodyBytes))
	}

	// Get user info
	userEndpoint := "https://conn.nodeloc.cc/oauth2/userinfo"
	req, err = http.NewRequest("GET", userEndpoint, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Authorization", "Bearer "+tokenRes.AccessToken)
	req.Header.Set("Accept", "application/json")

	res2, err := client.Do(req)
	if err != nil {
		return nil, errors.New("failed to get user info from Nodeloc")
	}
	defer res2.Body.Close()

	var nodelocUser NodelocUser
	if err := json.NewDecoder(res2.Body).Decode(&nodelocUser); err != nil {
		return nil, err
	}

	return &nodelocUser, nil
}

func NodelocOAuth(c *gin.Context) {
	session := sessions.Default(c)

	errorCode := c.Query("error")
	if errorCode != "" {
		errorDescription := c.Query("error_description")
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": errorDescription,
		})
		return
	}

	state := c.Query("state")
	if state == "" || session.Get("oauth_state") == nil || state != session.Get("oauth_state").(string) {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "state is empty or not same",
		})
		return
	}

	username := session.Get("username")
	if username != nil {
		NodelocBind(c)
		return
	}

	if !common.NodelocOAuthEnabled {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "管理员未开启通过 Nodeloc 登录以及注册",
		})
		return
	}

	code := c.Query("code")
	nodelocUser, err := getNodelocUserInfoByCode(code, c)
	if err != nil {
		common.ApiError(c, err)
		return
	}

	user := model.User{
		NodelocId: strconv.Itoa(nodelocUser.Id),
	}
	if err := user.FillUserByNodelocId(); err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		// 如果用户不存在，创建新用户
		if common.RegisterEnabled {
			user.Username = "nodeloc_" + strconv.Itoa(model.GetMaxUserId()+1)
			user.DisplayName = nodelocUser.Name
			user.Role = common.RoleCommonUser
			user.Status = common.UserStatusEnabled

			affCode := session.Get("aff")
			inviterId := 0
			if affCode != nil {
				inviterId, _ = model.GetUserIdByAffCode(affCode.(string))
			}

			if err := user.Insert(inviterId); err != nil {
				c.JSON(http.StatusOK, gin.H{
					"success": false,
					"message": err.Error(),
				})
				return
			}
		} else {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "管理员关闭了新用户注册",
			})
			return
		}
	}

	if user.Status != common.UserStatusEnabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "用户已被封禁",
			"success": false,
		})
		return
	}

	setupLogin(&user, c)
}
