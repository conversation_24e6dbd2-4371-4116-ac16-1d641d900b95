{"主页": "Home", "文档": "Docs", "控制台": "<PERSON><PERSON><PERSON>", "$%.6f 额度": "$%.6f quota", "或": "or", "登 录": "Log In", "注 册": "Sign Up", "使用 邮箱或用户名 登录": "Sign in with <PERSON><PERSON> or <PERSON>rna<PERSON>", "使用 GitHub 继续": "Continue with GitHub", "使用 OIDC 继续": "Continue with OIDC", "使用 微信 继续": "Continue with WeChat", "使用 LinuxDO 继续": "Continue with LinuxDO", "使用 用户名 注册": "Sign up with <PERSON><PERSON><PERSON>", "其他登录选项": "Other login options", "其他注册选项": "Other registration options", "请输入您的用户名或邮箱地址": "Please enter your username or email address", "请输入您的邮箱地址": "Please enter your email address", "请输入您的密码": "Please enter your password", "继续": "Continue", "%d 点额度": "%d point quota", "尚未实现": "Not yet implemented", "余额不足": "Insufficient quota", "危险操作": "Dangerous operation", "输入你的账户名": "Enter your account name", "确认删除": "Confirm deletion", "确认绑定": "Confirm binding", "您正在删除自己的帐户，将清空所有数据且不可恢复": "You are deleting your account. All data will be cleared and cannot be recovered.", "通道「%s」（#%d）已被禁用": "Channel %s (#%d) has been disabled", "通道「%s」（#%d）已被禁用，原因：%s": "Channel %s (#%d) has been disabled, reason: %s", "测试已在运行中": "Test is already running", "响应时间 %.2fs 超过阈值 %.2fs": "Response time %.2fs exceeds threshold %.2fs", "通道测试完成": "Channel test completed", "通道测试完成，如果没有收到禁用通知，说明所有通道都正常": "Channel test completed. If no disable notification received, all channels are functioning normally", "无法连接至 GitHub 服务器，请稍后重试！": "Unable to connect to GitHub server. Please try again later!", "返回值非法，用户字段为空，请稍后重试！": "Invalid return value, user field is empty. Please try again later!", "管理员未开启通过 GitHub 登录以及注册": "Administrator has not enabled GitHub login and registration", "管理员关闭了新用户注册": "Administrator has disabled new user registration", "用户已被封禁": "User has been banned", "该 GitHub 账户已被绑定": "This GitHub account is already bound", "邮箱地址已被占用": "Email address is already in use", "%s邮箱验证邮件": "%s Email verification", "<p>您好，你正在进行%s邮箱验证。</p>": "<p>Hello, you are verifying your %s email.</p>", "<p>您的验证码为: <strong>%s</strong></p>": "<p>Your verification code is: <strong>%s</strong></p>", "<p>验证码 %d 分钟内有效，如果不是本人操作，请忽略。</p>": "<p>Verification code is valid for %d minutes. If you did not request this, please ignore.</p>", "无效的参数": "Invalid parameter", "该邮箱地址未注册": "This email address is not registered", "%s密码重置": "%s Password reset", "<p>您好，你正在进行%s密码重置。</p>": "<p>Hello, you are resetting your %s password.</p>", "<p>点击<a href='%s'>此处</a>进行密码重置。</p>": "<p>Click <a href='%s'>here</a> to reset your password.</p>", "<p>重置链接 %d 分钟内有效，如果不是本人操作，请忽略。</p>": "<p>Reset link is valid for %d minutes. If you did not request this, please ignore.</p>", "重置链接非法或已过期": "Reset link is invalid or expired", "无法启用 GitHub OAuth，请先填入 GitHub Client ID 以及 GitHub Client Secret！": "Unable to enable GitHub OAuth. Please enter GitHub Client ID and GitHub Client Secret first!", "无法启用微信登录，请先填入微信登录相关配置信息！": "Unable to enable WeChat login. Please enter WeChat login configuration first!", "无法启用 Turnstile 校验，请先填入 Turnstile 校验相关配置信息！": "Unable to enable Turnstile verification. Please enter Turnstile verification configuration first!", "兑换码名称长度必须在1-20之间": "Redemption code name must be between 1-20 characters", "兑换码个数必须大于0": "Number of redemption codes must be greater than 0", "一次兑换码批量生成的个数不能大于 100": "Cannot generate more than 100 redemption codes at once", "当前分组上游负载已饱和，请稍后再试": "Current group upstream load is saturated. Please try again later", "令牌名称过长": "Token name is too long", "令牌已过期，无法启用，请先修改令牌过期时间，或者设置为永不过期": "Token has expired and cannot be enabled. Please modify token expiration time or set to never expire", "令牌可用额度已用尽，无法启用，请先修改令牌剩余额度，或者设置为无限额度": "Token quota is depleted and cannot be enabled. Please modify remaining quota or set to unlimited", "管理员关闭了密码登录": "Administrator has disabled password login", "无法保存会话信息，请重试": "Unable to save session information. Please try again", "管理员关闭了通过密码进行注册，请使用第三方账户验证的形式进行注册": "Administrator has disabled password registration. Please register using third-party account verification", "输入不合法 ": "Invalid input ", "管理员开启了邮箱验证，请输入邮箱地址和验证码": "Administrator has enabled email verification. Please enter email address and verification code", "验证码错误或已过期": "Verification code is incorrect or expired", "无权获取同级或更高等级用户的信息": "No permission to access information of users at same or higher level", "请重试，系统生成的 UUID 竟然重复了！": "Please try again, system generated UUID is duplicated!", "输入不合法": "Invalid input", "无权更新同权限等级或更高权限等级的用户信息": "No permission to update user information at same or higher permission level", "管理员将用户额度从 %s修改为 %s": "Administrator modified user quota from %s to %s", "无权删除同权限等级或更高权限等级的用户": "No permission to delete users at same or higher permission level", "无法创建权限大于等于自己的用户": "Cannot create users with permissions greater than or equal to your own", "用户不存在": "User does not exist", "无法禁用超级管理员用户": "Cannot disable super administrator user", "无法删除超级管理员用户": "Cannot delete super administrator user", "普通管理员用户无法提升其他用户为管理员": "Regular administrator cannot promote other users to administrator", "该用户已经是管理员": "This user is already an administrator", "无法降级超级管理员用户": "Cannot downgrade super administrator user", "该用户已经是普通用户": "This user is already a regular user", "管理员未开启通过微信登录以及注册": "Administrator has not enabled WeChat login and registration", "该微信账号已被绑定": "This WeChat account is already bound", "无权进行此操作，未登录且未提供 access token": "No permission for this operation: not logged in and no access token provided", "无权进行此操作，access token 无效": "No permission for this operation: invalid access token", "无权进行此操作，权限不足": "No permission for this operation: insufficient permissions", "普通用户不支持指定渠道": "Regular users cannot specify channels", "无效的渠道 ID": "Invalid channel ID", "该渠道已被禁用": "This channel has been disabled", "无效的请求": "Invalid request", "无可用渠道": "No available channels", "Turnstile token 为空": "Turnstile token is empty", "Turnstile 校验失败，请刷新重试！": "Turnstile verification failed. Please refresh and try again!", "id 为空！": "ID is empty!", "未提供兑换码": "No redemption code provided", "无效的 user id": "Invalid user ID", "无效的兑换码": "Invalid redemption code", "该兑换码已被使用": "This redemption code has been used", "通过兑换码充值 %s": "Recharge %s via redemption code", "未提供令牌": "No token provided", "该令牌状态不可用": "This token status is unavailable", "该令牌已过期": "This token has expired", "该令牌额度已用尽": "This token's quota is depleted", "无效的令牌": "Invalid token", "id 或 userId 为空！": "ID or userID is empty!", "quota 不能为负数！": "Quota cannot be negative!", "令牌额度不足": "Insufficient token quota", "用户额度不足": "Insufficient user quota", "您的额度即将用尽": "Your quota is almost depleted", "您的额度已用尽": "Your quota is depleted", "%s，当前剩余额度为 %d，为了不影响您的使用，请及时充值。<br/>充值链接：<a href='%s'>%s</a>": "%s, current remaining quota is %d. To avoid service interruption, please recharge promptly.<br/>Recharge link: <a href='%s'>%s</a>", "affCode 为空！": "Affiliate code is empty!", "新用户注册赠送 %s": "New user registration bonus: %s", "使用邀请码赠送 %s": "Invitation code bonus: %s", "邀请用户赠送 %s": "Referral bonus: %s", "用户名或密码为空": "Username or password is empty", "用户名或密码错误，或用户已被封禁": "Username or password is incorrect, or user has been banned", "email 为空！": "Email is empty!", "GitHub id 为空！": "GitHub ID is empty!", "WeChat id 为空！": "WeChat ID is empty!", "username 为空！": "Username is empty!", "邮箱地址或密码为空！": "Email address or password is empty!", "OpenAI 接口聚合管理，支持多种渠道包括 Azure，可用于二次分发管理 key，仅单可执行文件，已打包好 Docker 镜像，一键部署，开箱即用": "OpenAI API aggregation management system supporting multiple channels including Azure. Can be used for key management and redistribution. Single executable file, pre-packaged Docker image, one-click deployment, ready to use", "未知类型": "Unknown type", "不支持": "Not supported", "操作成功完成！": "Operation completed successfully!", "已启用": "Enabled", "已禁用": "Disabled", "未知状态": "Unknown status", " 秒": "s", " 分钟 ": "m", " 小时 ": "h", " 天 ": "d", " 个月 ": "M", " 年 ": "y", "未测试": "Not tested", "通道 ${name} 测试成功，耗时 ${time.toFixed(2)} 秒。": "Channel ${name} test successful, took ${time.toFixed(2)} seconds.", "已成功开始测试所有已启用通道，请刷新页面查看结果。": "Successfully started testing all enabled channels. Please refresh page to view results.", "通道 ${name} 余额更新成功！": "Channel ${name} quota updated successfully!", "已更新完毕所有已启用通道余额！": "Updated quota for all enabled channels!", "渠道ID，名称，密钥，API地址": "Channel ID, name, key, Base URL", "名称": "Name", "分组": "Group", "类型": "Type", "状态": "Status", "响应时间": "Response time", "余额": "Balance", "操作": "Actions", "未更新": "Not updated", "测试": "Test", "更新余额": "Update balance", "删除": "Delete", "删除渠道 {channel.name}": "Delete channel {channel.name}", "禁用": "Disable", "启用": "Enable", "编辑": "Edit", "添加新的渠道": "Add new channel", "测试所有已启用通道": "Test all enabled channels", "更新所有已启用通道余额": "Update balance for all enabled channels", "刷新": "Refresh", "处理中...": "Processing...", "绑定成功！": "Binding successful!", "登录成功！": "Login successful!", "操作失败，重定向至登录界面中...": "Operation failed, redirecting to login page...", "出现错误，第 ${count} 次重试中...": "Error occurred, retry attempt ${count}...", "首页": "Home", "渠道": "Channels", "令牌": "Tokens", "兑换": "Redeem", "充值": "Recharge", "用户": "Users", "日志": "Logs", "设置": "Settings", "关于": "About", "价格": "Pricing", "聊天": "Cha<PERSON>", "注销成功!": "Logout successful!", "注销": "Logout", "登录": "Sign in", "注册": "Sign up", "未登录或登录已过期，请重新登录！": "Not logged in or session expired. Please login again!", "用户登录": "User Login", "密码": "Password", "忘记密码？": "Forgot password?", "点击重置": "Click to reset", "； 没有账户？": "; No account?", "点击注册": "Click to register", "微信扫码关注公众号，输入「验证码」获取验证码（三分钟内有效）": "Scan WeChat QR code to follow official account, enter \"verification code\" to get code (valid for 3 minutes)", "全部用户": "All users", "当前用户": "Current user", "全部'": "All'", "充值'": "Recharge'", "消费'": "Consume'", "管理'": "Manage'", "系统'": "System'", " 充值 ": " Recharge ", " 消费 ": " Consume ", " 管理 ": " Manage ", " 系统 ": " System ", " 未知 ": " Unknown ", "时间": "Time", "详情": "Details", "选择模式": "Select mode", "选择明细分类": "Select detail category", "模型倍率不是合法的 JSON 字符串": "Model ratio is not a valid JSON string", "通用设置": "General Settings", "充值链接": "Recharge Link", "例如发卡网站的购买链接": "E.g., purchase link from card issuing website", "文档地址": "Document Link", "例如 https://docs.newapi.pro": "E.g., https://docs.newapi.pro", "聊天页面链接": "<PERSON><PERSON>", "例如 ChatGPT Next Web 的部署地址": "E.g., ChatGPT Next Web deployment address", "单位美元额度": "Quota per USD", "一单位货币能兑换的额度": "Quota exchangeable per unit currency", "启用额度消费日志记录": "Enable quota consumption logging", "以货币形式显示额度": "Display quota as currency", "相关 API 显示令牌额度而非用户额度": "Related APIs show token quota instead of user quota", "保存通用设置": "Save General Set<PERSON>s", "监控设置": "Monitoring Settings", "测试所有渠道的最长响应时间": "Maximum response time for testing all channels", "单位秒": "Unit: seconds", "当运行通道全部测试时": "When running all channel tests", "超过此时间将自动禁用通道": "Channels exceeding this time will be automatically disabled", "额度提醒阈值": "Quota reminder threshold", "低于此额度时将发送邮件提醒用户": "Email reminder will be sent when quota falls below this", "失败时自动禁用通道": "Automatically disable channel on failure", "保存监控设置": "Save Monitoring Settings", "额度设置": "<PERSON><PERSON><PERSON>", "新用户初始额度": "Initial quota for new users", "例如": "For example", "请求预扣费额度": "Pre-deduction quota for requests", "请求结束后多退少补": "Adjust after request completion", "邀请新用户奖励额度": "Referral bonus quota", "新用户使用邀请码奖励额度": "New user invitation code bonus quota", "保存额度设置": "Save <PERSON><PERSON><PERSON>", "倍率设置": "<PERSON><PERSON>", "模型倍率": "Model ratio", "为一个 JSON 文本": "Is a JSON text", "键为模型名称": "Key is model name", "值为倍率": "Value is ratio", "分组倍率": "Group ratio", "键为分组名称": "Key is group name", "保存倍率设置": "Save <PERSON><PERSON>", "已是最新版本": "Is the latest version", "检查更新": "Check for updates", "公告": "Announcement", "在此输入新的公告内容，支持 Markdown & HTML 代码": "Enter the new announcement content here, supports Markdown & HTML code", "保存公告": "Save Announcement", "个性化设置": "Personalization Settings", "系统名称": "System Name", "在此输入系统名称": "Enter the system name here", "设置系统名称": "Set system name", "图片地址": "Image URL", "在此输入 Logo 图片地址": "Enter the Logo image URL here", "首页内容": "Home Page Content", "在此输入首页内容，支持 Markdown & HTML 代码，设置后首页的状态信息将不再显示。如果输入的是一个链接，则会使用该链接作为 iframe 的 src 属性，这允许你设置任意网页作为首页": "Enter the home page content here, supports <PERSON><PERSON>", "保存首页内容": "Save Home Page Content", "在此输入新的关于内容，支持 Markdown & HTML 代码。如果输入的是一个链接，则会使用该链接作为 iframe 的 src 属性，这允许你设置任意网页作为关于页面": "Enter new about content here, support <PERSON><PERSON>", "保存关于": "Save About", "移除 One API 的版权标识必须首先获得授权，项目维护需要花费大量精力，如果本项目对你有意义，请主动支持本项目": "Removal of One API copyright mark must first be authorized. Project maintenance requires a lot of effort. If this project is meaningful to you, please actively support it.", "页脚": "Footer", "在此输入新的页脚，留空则使用默认页脚，支持 HTML 代码": "Enter the new footer here, leave blank to use the default footer, supports HTML code.", "设置页脚": "<PERSON>", "新版本": "New Version", "关闭": "Close", "密码已重置并已复制到剪贴板：": "Password has been reset and copied to clipboard: ", "密码已复制到剪贴板：": "Password has been copied to clipboard: ", "密码重置确认": "Password Reset Confirmation", "邮箱地址": "Email address", "提交": "Submit", "等待获取邮箱信息...": "Waiting to get email information...", "确认重置密码": "Confirm Password Reset", "无效的重置链接，请重新发起密码重置请求": "Invalid reset link, please initiate a new password reset request", "请输入邮箱地址": "Please enter the email address", "请稍后几秒重试": "Please retry in a few seconds", "正在检查用户环境": "Checking user environment", "重置邮件发送成功": "Reset mail sent successfully", "请检查邮箱": "Please check your email", "密码重置": "Password Reset", "令牌已重置并已复制到剪贴板": "Token has been reset and copied to clipboard", "邀请链接已复制到剪切板": "Invitation link has been copied to clipboard", "微信账户绑定成功": "WeChat account binding succeeded", "验证码发送成功": "Verification code sent successfully", "邮箱账户绑定成功": "Email account binding succeeded", "注意": "Note", "此处生成的令牌用于系统管理": "The token generated here is used for system management", "而非用于请求 OpenAI 相关的服务": "Not for requesting OpenAI related services", "请知悉": "Please be aware", "更新个人信息": "Update Personal Information", "生成系统访问令牌": "Generate System Access Token", "复制邀请链接": "Copy Invitation Link", "账号绑定": "Account Binding", "绑定微信账号": "Bind <PERSON><PERSON><PERSON> Account", "微信扫码关注公众号": "<PERSON>an the QR code with WeChat to follow the official account", "输入": "Enter", "验证码": "Verification Code", "获取验证码": "Get Verification Code", "三分钟内有效": "Valid for three minutes", "绑定": "Bind", "绑定 GitHub 账号": "Bind <PERSON><PERSON><PERSON><PERSON> Account", "绑定邮箱地址": "Bind Email Address", "输入邮箱地址": "Enter Email Address", "未使用": "Unused", "已使用": "Used", "操作成功完成": "Operation successfully completed", "搜索兑换码的 ID 和名称": "Search for ID and name", "额度": "<PERSON><PERSON><PERSON>", "创建时间": "Creation Time", "兑换时间": "Redemption Time", "尚未兑换": "Not yet redeemed", "已复制到剪贴板": "Copied to clipboard", "无法复制到剪贴板": "Unable to copy to clipboard", "请手动复制": "Please copy manually", "已将兑换码填入搜索框": "The voucher code has been filled into the search box", "复制": "Copy", "添加新的兑换码": "Add a new voucher", "密码长度不得小于 8 位": "Password length must not be less than 8 characters", "两次输入的密码不一致": "The two passwords entered do not match", "注册成功": "Registration succeeded", "请稍后几秒重试，Turnstile 正在检查用户环境": "Please retry in a few seconds, Turnstile is checking user environment", "验证码发送成功，请检查你的邮箱": "Verification code sent successfully, please check your email", "新用户注册": "New User Registration", "输入用户名，最长 12 位": "Enter username, up to 12 characters", "输入密码，最短 8 位，最长 20 位": "Enter password, at least 8 characters and up to 20 characters", "输入验证码": "Enter Verification Code", "已有账户": "Already have an account", "点击登录": "Click to log in", "服务器地址": "Server Address", "更新服务器地址": "Update Server Address", "配置登录注册": "Configure Login/Registration", "允许通过密码进行登录": "Allow login via password", "允许通过密码进行注册": "Allow registration via password", "通过密码注册时需要进行邮箱验证": "Email verification is required when registering via password", "允许通过 GitHub 账户登录 & 注册": "Allow login & registration via GitHub account", "允许通过微信登录 & 注册": "Allow login & registration via WeChat", "允许新用户注册（此项为否时，新用户将无法以任何方式进行注册": "Allow new user registration (if this option is off, new users will not be able to register in any way", "启用 Turnstile 用户校验": "Enable Turnstile user verification", "配置 SMTP": "Configure SMTP", "用以支持系统的邮件发送": "To support the system email sending", "SMTP 服务器地址": "SMTP Server Address", "例如：smtp.qq.com": "For example: smtp.qq.com", "SMTP 端口": "SMTP Port", "默认: 587": "Default: 587", "SMTP 账户": "SMTP Account", "通常是邮箱地址": "Usually an email address", "发送者邮箱": "Sender email", "通常和邮箱地址保持一致": "Usually consistent with the email address", "SMTP 访问凭证": "SMTP Access Credential", "敏感信息不会发送到前端显示": "Sensitive information will not be displayed in the frontend", "保存 SMTP 设置": "Save SMTP Settings", "配置 GitHub OAuth App": "Configure GitHub OAuth App", "用以支持通过 GitHub 进行登录注册": "To support login & registration via GitHub", "点击此处": "click here", "管理你的 GitHub OAuth App": "Manage your GitHub OAuth App", "输入你注册的 GitHub OAuth APP 的 ID": "Enter your registered GitHub OAuth APP ID", "保存 GitHub OAuth 设置": "Save GitHub OAuth Settings", "配置 WeChat Server": "Configure WeChat Server", "用以支持通过微信进行登录注册": "To support login & registration via WeChat", "了解 WeChat Server": "Learn about WeChat Server", "WeChat Server 访问凭证": "WeChat Server Access Credential", "微信公众号二维码图片链接": "WeChat Public Account QR Code Image Link", "输入一个图片链接": "Enter an image link", "保存 WeChat Server 设置": "Save WeChat Server Settings", "配置 Turnstile": "Configure Turn<PERSON><PERSON>", "用以支持用户校验": "To support user verification", "管理你的 Turnstile Sites，推荐选择 Invisible Widget Type": "Manage your Turnstile Sites, recommend selecting Invisible Widget Type", "输入你注册的 Turnstile Site Key": "Enter your registered Turnstile Site Key", "保存 Turnstile 设置": "Save Turn<PERSON><PERSON> Settings", "已过期": "Expired", "已耗尽": "Exhausted", "搜索令牌的名称 ...": "Search for the name of the token...", "已用额度": "Quo<PERSON> used", "剩余额度": "Remaining quota", "总额度": "Total quota", "智能熔断": "Smart fallback", "当前分组为 auto，会自动选择最优分组，当一个组不可用时自动降级到下一个组（熔断机制）": "The current group is auto, it will automatically select the optimal group, and automatically downgrade to the next group when a group is unavailable (breakage mechanism)", "过期时间": "Expiration time", "无": "None", "无限制": "Unlimited", "永不过期": "Never expires", "无法复制到剪贴板，请手动复制，已将令牌填入搜索框": "Unable to copy to clipboard, please copy manually, the token has been entered into the search box", "删除令牌": "Delete Token", "添加新的令牌": "Add New Token", "普通用户": "Normal User", "管理员": "Admin", "超级管理员": "Super Admin", "未知身份": "Unknown Identity", "已激活": "Activated", "已封禁": "Banned", "搜索用户的 ID，用户名，显示名称，以及邮箱地址 ...": "Search user ID, username, display name, and email address...", "用户名": "Username", "统计信息": "Statistics", "用户角色": "User Role", "未绑定邮箱地址": "Email not bound", "请求次数": "Number of Requests", "提升": "Promote", "降级": "Demote", "删除用户": "Delete User", "添加新的用户": "Add New User", "自定义": "Custom", "等价金额：": "Equivalent Amount: ", "未登录或登录已过期，请重新登录": "Not logged in or login has expired, please log in again", "请求次数过多，请稍后再试": "Too many requests, please try again later", "服务器内部错误，请联系管理员": "Server internal error, please contact the administrator", "本站仅作演示之用，无服务端": "This site is for demonstration purposes only, no server-side", "超级管理员未设置充值链接！": "Super administrator has not set the recharge link!", "错误：": "Error: ", "新版本可用：${data.version}，请使用快捷键 Shift + F5 刷新页面": "New version available: ${data.version}, please refresh the page using shortcut Shift + F5", "无法正常连接至服务器": "Unable to connect to the server normally", "管理渠道": "Manage Channels", "系统状况": "System Status", "系统信息": "System Information", "系统信息总览": "System Information Overview", "版本": "Version", "源码": "Source Code", "启动时间": "Startup Time", "系统配置": "System Configuration", "系统配置总览": "System Configuration Overview", "邮箱验证": "Email Verification", "未启用": "Not Enabled", "GitHub 身份验证": "GitHub Authentication", "微信身份验证": "WeChat Authentication", "Turnstile 用户校验": "Turnstile User Verification", "创建新的渠道": "Create New Channel", "是否自动禁用": "Whether to automatically disable", "仅当自动禁用开启时有效，关闭后不会自动禁用该渠道": "Only effective when automatic disabling is enabled, after closing, the channel will not be automatically disabled", "镜像": "Mirror", "请输入镜像站地址，格式为：https://domain.com，可不填，不填则使用渠道默认值": "Please enter the mirror site address, the format is: https://domain.com, it can be left blank, if left blank, the default value of the channel will be used", "模型": "Model", "请选择该通道所支持的模型": "Please select the model supported by the channel", "填入基础模型": "Fill in the basic model", "填入所有模型": "Fill in all models", "清除所有模型": "Clear all models", "复制所有模型": "Copy all models", "密钥": "Key", "请输入密钥": "Please enter the key", "批量创建": "Batch Create", "更新渠道信息": "Update Channel Information", "我的令牌": "My Tokens", "管理兑换码": "Manage Redeem Codes", "兑换码": "Redeem Code", "管理用户": "Manage Users", "额度明细": "<PERSON><PERSON><PERSON>", "个人设置": "Personal Settings", "运营设置": "Operation Settings", "系统设置": "System Settings", "其他设置": "Other Settings", "项目仓库地址": "Project Repository Address", "可在设置页面设置关于内容，支持 HTML & Markdown": "The About content can be set on the settings page, supporting HTML & Markdown", "由": "developed by", "开发，基于": "based on", "MIT 协议": "MIT License", "充值额度": "<PERSON><PERSON><PERSON>", "获取兑换码": "Get Redeem Code", "一个月后过期": "Expires after one month", "一天后过期": "Expires after one day", "一小时后过期": "Expires after one hour", "一分钟后过期": "Expires after one minute", "创建新的令牌": "Create New Token", "令牌分组，默认为用户的分组": "Token group, default is the your's group", "IP白名单": "IP whitelist", "令牌的额度仅用于限制令牌本身的最大额度使用量，实际的使用受到账户的剩余额度限制": "The quota of the token is only used to limit the maximum quota usage of the token itself, and the actual usage is limited by the remaining quota of the account", "无限额度": "Unlimited quota", "更新令牌信息": "Update Token Information", "请输入充值码！": "Please enter the recharge code!", "请输入名称": "Please enter a name", "请输入密钥，一行一个": "Please enter the key, one per line", "请输入额度": "Please enter the quota", "令牌创建成功": "<PERSON>ken created successfully", "令牌更新成功": "Token updated successfully", "充值成功！": "Recharge successful!", "更新用户信息": "Update User Information", "请输入新的用户名": "Please enter a new username", "请输入新的密码": "Please enter a new password", "显示名称": "Display Name", "请输入新的显示名称": "Please enter a new display name", "已绑定的 GITHUB 账户": "<PERSON><PERSON> GitHub Account", "已绑定的 WECHAT 账户": "Bound WeChat Account", "已绑定的 EMAIL 账户": "Bound Email Account", "已绑定的 TELEGRAM 账户": "Bound Telegram Account", "此项只读，要用户通过个人设置页面的相关绑定按钮进行绑定，不可直接修改": "This item is read-only. Users need to bind through the relevant binding button on the personal settings page, and cannot be modified directly", "用户信息更新成功！": "User information updated successfully!", "使用明细（总消耗额度：{renderQuota(stat.quota)}）": "Usage Details (Total Consumption Quota: {renderQuota(stat.quota)})", "用户名称": "User Name", "令牌名称": "Token Name", "留空则查询全部用户": "Leave blank to query all users", "留空则查询全部令牌": "Leave blank to query all tokens", "模型名称": "Model Name", "留空则查询全部模型": "Leave blank to query all models", "起始时间": "Start Time", "结束时间": "End Time", "查询": "Query", "提示": "Prompt", "补全": "Completion", "消耗额度": "Used Quota", "渠道不存在：%d": "Channel does not exist: %d", "数据库一致性已被破坏，请联系管理员": "Database consistency has been broken, please contact the administrator", "使用近似的方式估算 token 数以减少计算量": "Estimate the number of tokens in an approximate way to reduce computational load", "请填写ChannelName和ChannelKey！": "Please fill in the ChannelName and ChannelKey!", "请至少选择一个Model！": "Please select at least one Model!", "加载关于内容失败": "Failed to load content about", "用户账户创建成功！": "User account created successfully!", "生成数量": "Generate quantity", "请输入生成数量": "Please enter the quantity to generate", "创建新用户账户": "Create new user account", "渠道更新成功！": "Channel updated successfully!", "渠道创建成功！": "Channel created successfully!", "请选择分组": "Please select a group", "更新兑换码信息": "Update redemption code information", "创建新的兑换码": "Create a new redemption code", "未找到所请求的页面": "The requested page was not found", "过期时间格式错误！": "Expiration time format error!", "过期时间不能早于当前时间！": "Expiration time cannot be earlier than the current time!", "请输入过期时间，格式为 yyyy-MM-dd HH:mm:ss，-1 表示无限制": "Please enter the expiration time, the format is yyyy-MM-dd HH:mm:ss, -1 means no limit", "此项可选，为一个 JSON 文本，键为用户请求的模型名称，值为要替换的模型名称，例如：": "This is optional, it's a JSON text, the key is the model name requested by the user, and the value is the model name to be replaced, for example:", "此项可选，输入镜像站地址，格式为：": "This is optional, enter the mirror site address, the format is:", "模型映射": "Model mapping", "请输入默认 API 版本，例如：2023-03-15-preview，该配置可以被实际的请求查询参数所覆盖": "Please enter the default API version, for example: 2023-03-15-preview, this configuration can be overridden by the actual request query parameters", "默认": "<PERSON><PERSON><PERSON>", "图片演示": "Image demo", "注意，系统请求的时模型名称中的点会被剔除，例如：gpt-4.1会请求为gpt-41，所以在Azure部署的时候，部署模型名称需要手动改为gpt-41": "Note that the dot in the model name requested by the system will be removed, for example: gpt-4.1 will be requested as gpt-41, so when deploying on Azure, the deployment model name needs to be manually changed to gpt-41", "2025年5月10日后添加的渠道，不需要再在部署的时候移除模型名称中的\".\"": "After May 10, 2025, channels added do not need to remove the dot in the model name during deployment", "模型映射必须是合法的 JSON 格式！": "Model mapping must be in valid JSON format!", "取消": "Cancel", "重置": "Reset", "请输入新的剩余额度": "Please enter the new remaining quota", "请输入单个兑换码中包含的额度": "Please enter the quota included in a single redemption code", "请输入用户名": "Please enter username", "请输入显示名称": "Please enter display name", "请输入密码": "Please enter password", "注意，模型部署名称必须和模型名称保持一致": "Note that the model deployment name must be consistent with the model name", "请输入 AZURE_OPENAI_ENDPOINT": "Please enter AZURE_OPENAI_ENDPOINT", "请输入自定义渠道的 Base URL": "Please enter the Base URL of the custom channel", "Homepage URL 填": "Fill in the Homepage URL", "Authorization callback URL 填": "Fill in the Authorization callback URL", "请为通道命名": "Please name the channel", "此项可选，用于修改请求体中的模型名称，为一个 JSON 字符串，键为请求中模型名称，值为要替换的模型名称，例如：": "This is optional, used to modify the model name in the request body, it's a JSON string, the key is the model name in the request, and the value is the model name to be replaced, for example:", "模型重定向": "Model redirection", "请输入渠道对应的鉴权密钥": "Please enter the authentication key corresponding to the channel", "注意，": "Note that, ", "，图片演示。": "related image demo.", "令牌创建成功，请在列表页面点击复制获取令牌！": "<PERSON><PERSON> created successfully, please click copy on the list page to get the token!", "代理": "Proxy", "此项可选，用于通过自定义API地址来进行 API 调用，请输入API地址，格式为：https://domain.com": "This is optional, used to make API calls through the proxy site, please enter the proxy site address, the format is: https://domain.com", "取消密码登录将导致所有未绑定其他登录方式的用户（包括管理员）无法通过密码登录，确认取消？": "Canceling password login will cause all users (including administrators) who have not bound other login methods to be unable to log in via password, confirm cancel?", "按照如下格式输入：": "Enter in the following format:", "模型版本": "Model version", "请输入星火大模型版本，注意是接口地址中的版本号，例如：v2.1": "Please enter the version of the Starfire model, note that it is the version number in the interface address, for example: v2.1", "点击查看": "click to view", "请确保已在 Azure 上创建了 gpt-35-turbo 模型，并且 apiVersion 已正确填写！": "Please make sure that the gpt-35-turbo model has been created on Azure, and the apiVersion has been filled in correctly!", "建议收藏所有地址，以防失联。": "It is recommended to bookmark all addresses to prevent losing contact.", "无法正常请求API的用户，请联系管理员。": "For users who cannot request the API normally, please contact the administrator.", "温馨提示": "Kind tips", "获取API URL列表时发生错误，请稍后重试。": "An error occurred while retrieving the API URL list, please try again later.", "，时间：": ",time:", "已用/剩余": "Used/Remaining", "，点击更新": ", click Update", "确定是否要清空此渠道记录额度？": "Are you sure you want to clear the record quota of this channel?", "此修改将不可逆": "This modification will be irreversible", "优先级": "Priority", "权重": "Weight", "测试操作项目组": "Test operation project team", "确定是否要删除此渠道？": "Are you sure you want to delete this channel?", "确定是否要复制此渠道？": "Are you sure you want to copy this channel?", "复制渠道的所有信息": "Copy all information for a channel", "展开操作": "Expand operation", "_复制": "_copy", "渠道未找到，请刷新页面后重试。": "Channel not found, please refresh the page and try again.", "渠道复制成功": "Channel copy successful", "渠道复制失败: ": "Channel copy failed:", "已成功开始测试所有通道，请刷新页面查看结果。": "Testing of all channels has been started successfully, please refresh the page to view the results.", "请先选择要删除的通道！": "Please select the channel you want to delete first!", "搜索渠道关键词": "Search channel keywords", "模型关键字": "model keyword", "选择分组": "Select group", "使用ID排序": "Sort by ID", "是否用ID排序": "Whether to sort by ID", "确定？": "Sure?", "确定是否要删除禁用通道？": "Are you sure you want to delete the disabled channel?", "开启批量操作": "Enable batch selection", "是否开启批量操作": "Whether to enable batch selection", "确定是否要删除所选通道？": "Are you sure you want to delete the selected channels?", "确定是否要修复数据库一致性？": "Are you sure you want to repair database consistency?", "进行该操作时，可能导致渠道访问错误，请仅在数据库出现问题时使用": "When performing this operation, it may cause channel access errors. Please only use it when there is a problem with the database.", "当前没有可用的启用令牌，请确认是否有令牌处于启用状态！": "There are currently no enablement tokens available, please confirm if one is enabled!", "API令牌": "API Token", "使用日志": "Usage log", "Midjourney日志": "Midjourney", "数据看板": "Dashboard", "模型列表": "Model list", "常见问题": "FAQ", "免费体验": "Free trial", "新用户注册赠送$": "Free $ for new user registration", "测试金额": "Test amount", "请稍后几秒重试，Turnstile 正在检查用户环境！": "Please try again in a few seconds, Turnstile is checking the user environment!", "您正在使用默认密码！": "You are using the default password!", "请立刻修改默认密码！": "Please change the default password immediately!", "请输入用户名和密码！": "Please enter username and password!", "用户名或邮箱": "Username or email", "微信扫码登录": "WeChat scan code to log in", "刷新成功": "Refresh successful", "刷新失败": "Refresh failed", "用时/首字": "Time/first word", "重试": "Retry", "用户信息": "User information", "无法复制到剪贴板，请手动复制": "Unable to copy to clipboard, please copy manually", "消费": "Consume", "管理": "Manage", "系统": "System", "用时": "time", "首字时间": "First word time", "是否流式": "Whether to stream", "非流": "not stream", "渠道 ID": "Channel ID", "用户ID": "User ID", "花费": "Spend", "列设置": "Column settings", "补偿": "compensate", "错误": "mistake", "未知": "unknown", "全选": "Select all", "组名必须唯一": "Group name must be unique", "解析 JSON 出错:": "Error parsing JSON:", "解析 GroupModel 时发生错误: ": "An error occurred while parsing GroupModel:", "GroupModel 未定义，无法更新分组": "GroupModel is not defined, cannot update grouping", "重置成功": "Reset successful", "加载数据出错:": "Error loading data:", "加载数据时发生错误: ": "An error occurred while loading data:", "保存成功": "Saved successfully", "部分保存失败，请重试": "Partial saving failed, please try again", "请检查输入": "Please check your input", "如何区分不同分组不同模型的价格：供参考的配置方式": "How to distinguish the prices of different models in different groups: configuration method for reference", "获取价格顺序": "Get price order", "确定同步远程数据吗？": "Are you sure you want to synchronize remote data?", "此修改将不可逆！建议同步前先备份自己的设置！": "This modification will be irreversible! It is recommended to back up your settings before synchronizing!", "模型固定价格(按次计费模型用)": "Model fixed price (for pay-per-view models)", "模型倍率(按量计费模型用)": "Model magnification (for pay-as-you-go model)", "为一个 JSON 文本，键为模型名称，值为倍率": "is a JSON text, the key is the model name, and the value is the magnification", "隐藏": "<PERSON>de", "分组名称": "Group name", "提交结果": "Results", "模式": "Mode", "任务状态": "Status", "耗时": "Time consuming", "结果图片": "Result", "失败原因": "Failure reason", "全部": "All", "成功": "Success", "未启动": "No start", "队列中": "In queue", "窗口等待": "window wait", "失败": "Failed", "绘图": "Drawing", "绘图日志": "Drawing log", "放大": "Upscalers", "微妙放大": "Upscale (Subtle)", "创造放大": "<PERSON><PERSON><PERSON> (Creative)", "强变换": "Low Variation", "弱变换": "High Variation", "图生文": "Describe", "图混合": "Blend", "重绘": "Vary", "局部重绘-提交": "Vary Region", "自定义变焦-提交": "Custom Zoom-Submit", "窗口处理": "window handling", "缩词后生图": "epigenetic diagram of abbreviation", "图生文按钮生图": "Picture and text button", "任务 ID": "Task ID", "速度模式": "speed mode", "错误：未登录或登录已过期，请重新登录！": "Error: Not logged in or your login has expired, please log in again!", "错误：请求次数过多，请稍后再试！": "Error: Too many requests, please try again later!", "错误：服务器内部错误，请联系管理员！": "Error: Internal server error, please contact the administrator!", "本站仅作演示之用，无服务端！": "This site is for demonstration purposes only, no server!", "已用额度：": "Used amount:", "请求次数：": "Number of requests:", "平移": "Pan", "上传文件": "Upload", "图生文后生图": "Pictures give rise to text and later pictures", "已提交": "Submitted", "重复提交": "Duplicate submission", "未提交": "Not submitted", "缩词": "<PERSON>en", "变焦": "zoom", "按次计费": "Pay per view", "按量计费": "Pay as you go", "标签": "Label", "人民币": "RMB", "说明": "illustrate", "可用性": "Availability", "数据加载失败": "Data loading failed", "发生错误，请重试": "An error occurred, please try again", "本站汇率1美金=": "The exchange rate of this site is 1 USD =", "模糊搜索": "fuzzy search", "选择标签": "Select label", "令牌分组": "Token grouping", "隐": "hidden", "本站当前已启用模型": "The model is currently enabled on this site", "个": "indivual", "倍率是本站的计算方式，不同模型有着不同的倍率，并非官方价格的多少倍，请务必知晓。": "The magnification is the calculation method of this website. Different models have different magnifications, which are not multiples of the official price. Please be sure to know.", "所有各厂聊天模型请统一使用OpenAI方式请求，支持OpenAI官方库<br/>Claude()Claude官方格式请求": "Please use the OpenAI method to request all chat models from each factory, and support the OpenAI official library<br/><PERSON>()Claude official format request", "复制选中模型": "Copy selected model", "分组说明": "Group description", "倍率是为了方便换算不同价格的模型": "The magnification is to facilitate the conversion of models with different prices.", "点击查看倍率说明": "Click to view the magnification description", "显": "show", "当前分组可用": "Available in current group", "当前分组不可用": "The current group is unavailable", "提示：": "input:", "输入：": "input:", "补全：": "output:", "输出：": "output:", "图片输出：": "Image output:", "模型价格：": "Model price:", "模型：": "Model:", "分组：": "Grouping:", "最终价格": "final price", "计费类型": "Billing type", "美元": "Dollar", "倍率": "<PERSON><PERSON>", "常见问题不是合法的 JSON 字符串": "FAQ is not a valid JSON string", "常见问题更新失败": "FAQ update failed", "活动内容已更新": "Event content has been updated", "活动内容更新失败": "Event content update failed", "页脚内容已更新": "Footer content updated", "页脚内容更新失败": "Footer content update failed", "Logo 图片地址": "Logo image address", "在此输入图片地址": "Enter image address here", "在此输入首页内容，支持 Markdown & HTML 代码，设置后首页的状态信息将不再显示。如果输入的是一个链接，则会使用该链接作为 iframe 的 src 属性，这允许你设置任意网页作为首页。": "Enter the home page content here, support <PERSON><PERSON>", "令牌分组说明": "Token grouping description", "在此输入新的关于内容，支持 Markdown & HTML 代码。如果输入的是一个链接，则会使用该链接作为 iframe 的 src 属性，这允许你设置任意网页作为关于页面。": "Enter new about content here, support <PERSON><PERSON>", "API地址列表": "API address list", "在此输入新的常见问题，json格式；键为问题，值为答案。": "Enter a new FAQ here in json format; the key is the question and the value is the answer.", "活动内容": "Activity content", "在此输入新的活动内容。": "Enter new event content here.", "总计": "Total", "无数据": "No data", "小时": "Hour", "新密码": "New Password", "重置邮件发送成功，请检查邮箱！": "The reset email was sent successfully, please check your email!", "输入你的账户名{{username}}以确认删除": "Enter your account name{{username}}to confirm deletion", "账户已删除！": "Account has been deleted!", "微信账户绑定成功！": "WeChat account bound successfully!", "两次输入的密码不一致！": "The passwords entered twice are inconsistent!", "密码修改成功！": "Password changed successfully!", "划转金额最低为": "The minimum transfer amount is", "请输入邮箱！": "Please enter your email!", "验证码发送成功，请检查邮箱！": "The verification code was sent successfully, please check your email!", "请输入邮箱验证码！": "Please enter the email verification code!", "请输入要划转的数量": "Please enter the amount to be transferred", "当前余额": "Current balance", "单独并发限制": "Individual concurrency limits", "未设置单独并发限制": "No individual concurrency limit is set", "无效的用户单独并发限制数据": "Invalid user individual concurrency limit data", "未绑定": "Not bound", "修改绑定": "Modify binding", "确认新密码": "Confirm new password", "历史消耗": "Consumption", "查看": "Check", "修改密码": "Change password", "删除个人账户": "Delete personal account", "已绑定": "Bound", "获取二维码失败": "Failed to obtain QR code", "获取当前设置失败": "Failed to get current settings", "设置已更新": "Settings updated", "更新设置失败": "Update settings failed", "确认解绑": "Confirm unbinding", "您确定要解绑WxPusher吗？": "Are you sure you want to unbind <PERSON><PERSON><PERSON><PERSON><PERSON>?", "解绑失败": "Unbinding failed", "订阅事件": "Subscribe to events", "通知方式": "Notification method", "留空将通知到账号邮箱": "Leave this blank to be notified to the account email", "查看接入文档": "View access documentation", "企业微信机器人Key": "Enterprise WeChat Robot Key", "您已绑定WxPusher，可以点击下方解绑": "You have bound <PERSON><PERSON><PERSON><PERSON><PERSON>, you can click below to unbind", "请扫描二维码绑定WxPusher": "Please scan the QR code to bind WxPusher", "预警额度（需订阅事件）": "Alert quota (need to subscribe to events)", " 时，将收到预警邮件（2小时最多1次）": "When, you will receive an early warning email (maximum once every 2 hours)", "兑换人ID": "Redeemer ID", "确定是否要删除此兑换码？": "Are you sure you want to delete this redemption code?", "已复制到剪贴板！": "Copied to clipboard!", "搜索关键字": "Search keywords", "关键字(id或者名称)": "Keyword (id or name)", "复制所选兑换码": "Copy selected redemption code", "请至少选择一个兑换码！": "Please select at least one redemption code!", "密码长度不得小于 8 位！": "Password must be at least 8 characters long!", "注册成功！": "Registration successful!", "验证码发送成功，请检查你的邮箱！": "The verification code was sent successfully, please check your email!", "确认密码": "Confirm Password", "邀请码": "Invitation code", "输入邀请码": "Enter invitation code", "账户": "Account", "邮箱": "Email", "已有账户？": "Already have an account?", "创意任务": "Tasks", "用户管理": "User Management", "任务ID（点击查看详情）": "Task ID (click to view details)", "进度": "schedule", "花费时间": "spend time", "生成音乐": "generate music", "生成歌词": "Generate lyrics", "歌曲拼接": "song splicing", "上传歌曲": "Upload songs", "生成视频": "Generate video", "扩展视频": "Extended video", "获取无水印": "Get no watermark", "生成图片": "Generate pictures", "可灵": "<PERSON><PERSON>", "即梦": "<PERSON><PERSON>", "正在提交": "Submitting", "执行中": "processing", "平台": "platform", "排队中": "Queuing", "已启用：限制模型": "Enabled: restricted model", "AMA 问天": "AMA Wentian", "项目操作按钮组": "Project action button group", "AMA 问天（BotGem）": "AMA Wentian (BotGem)", "确定是否要删除此令牌？": "Are you sure you want to delete this token?", "管理员未设置聊天链接": "The administrator has not set up a chat link", "复制所选令牌": "<PERSON><PERSON> selected token", "请至少选择一个令牌！": "Please select at least one token!", "管理员未设置查询页链接": "The administrator has not set the query page link", "批量删除令牌": "Batch delete token", "确定要删除所选的 {{count}} 个令牌吗？": "Are you sure you want to delete the selected {{count}} tokens?", "删除所选令牌": "Delete selected token", "请先选择要删除的令牌！": "Please select the token to be deleted!", "已删除 {{count}} 个令牌！": "Deleted {{count}} tokens!", "删除失败": "Delete failed", "复制令牌": "Copy token", "请选择你的复制方式": "Please select your copy method", "名称+密钥": "Name + key", "仅密钥": "Only key", "查看API地址": "View API address", "打开查询页": "Open query page", "时间(仅显示近3天)": "Time (only displays the last 3 days)", "请输入兑换码！": "Please enter the redemption code!", "兑换成功！": "Redemption successful!", "成功兑换额度：": "Successful redemption amount:", "请求失败": "Request failed", "管理员未开启在线充值！": "The administrator has not enabled online recharge!", "充值数量不能小于": "The recharge amount cannot be less than", "管理员未开启Stripe在线充值！": "The administrator has not enabled <PERSON>e online recharge!", "当前充值1美金=": "Current recharge = 1 USD =", "请选择充值方式！": "Please choose a recharge method!", "元": "CNY", "充值记录": "Recharge record", "返利记录": "Rebate record", "确定要充值 $": "Confirm to top up $", "兑换中...": "Redemming", "微信/支付宝 实付金额：": "WeChat/Alipay actual payment amount:", "Stripe 实付金额：": "Stripe actual payment amount:", "支付中...": "Paying", "支付宝": "Alipay", "待使用收益": "Proceeds to be used", "邀请人数": "Number of people invited", "兑换码充值": "Redemption code recharge", "使用兑换码快速充值": "Use redemption code to quickly recharge", "支付方式": "Payment method", "邀请奖励": "Invite reward", "或输入自定义金额": "Or enter a custom amount", "选择充值额度": "Select recharge amount", "实付": "Actual payment", "快速方便的充值方式": "Quick and convenient recharge method", "邀请好友获得额外奖励": "Invite friends to get additional rewards", "邀请好友注册，好友充值后您可获得相应奖励": "Invite friends to register, and you can get the corresponding reward after the friend recharges", "通过划转功能将奖励额度转入到您的账户余额中": "Transfer the reward amount to your account balance through the transfer function", "邀请的好友越多，获得的奖励越多": "The more friends you invite, the more rewards you will get", "在线充值": "Online recharge", "充值数量，最低 ": "Recharge quantity, minimum", "请选择充值金额": "Please select the recharge amount", "微信": "WeChat", "邀请返利": "Invite rebate", "总收益": "total revenue", "邀请信息": "Invitation information", "代理加盟": "Agent to join", "代理商信息": "Agent information", "分红记录": "Dividend record", "提现记录": "Withdrawal records", "代理商管理": "Agent management", "自定义输入": "custom input", "加载token失败": "Failed to load token", "配置聊天": "Configure chat", "模型消耗分布": "Model consumption distribution", "模型调用次数占比": "Model call ratio", "用户消耗分布": "User consumption distribution", "时间粒度": "Time granularity", "天": "day", "模型概览": "Model overview", "用户概览": "User overview", "正在策划中": "Under planning", "请求首页内容失败": "Requesting homepage content failed", "返回首页": "Return to home page", "获取用户数据时发生错误，请稍后重试。": "An error occurred while retrieving user data, please try again later.", "无额度": "No limit", "累计消费": "Accumulated consumption", "累计请求": "Cumulative requests", "你好，": "Hello,", "线路监控": "line monitoring", "查看全部": "View all", "异常": "Abnormal", "的未命名令牌": "unnamed token", "令牌更新成功！": "Token updated successfully!", "(origin) Discord原链接": "(origin) Discord original link", "请选择过期时间": "Please select expiration time", "数量": "quantity", "请选择或输入创建令牌的数量": "Please select or enter the number of tokens to create", "请选择渠道": "Please select a channel", "允许的IP，一行一个，不填写则不限制": "Allowed IPs, one per line, not filled in means no restrictions", "IP黑名单": "IP blacklist", "不允许的IP，一行一个": "IPs not allowed, one per line", "请选择该渠道所支持的模型": "Please select the model supported by this channel", "次": "times", "达到限速报错内容": "Error content when the speed limit is reached", "不填则使用默认报错": "If not filled in, the default error will be reported.", "Midjouney 设置 (可选)": "Midjouney settings (optional)", "令牌纬度控制 Midjouney 配置，设置优先级：令牌 {": "Token latitude controls Midjouney configuration, setting priority: token {", "图片代理地址最好用自己的，本站绘图量大，公用代理地址可能有时网速不佳": "It is best to use your own image proxy address. This site has a large amount of drawings, and public proxy addresses may sometimes have poor network speeds.", "【突发备用号池】用于应对高强度风控情况，当普通号池全部重试失败，任务进入备用号池执行并额外计费。": "[Sudden backup number pool] is used to deal with high-intensity risk control situations. When all retries in the ordinary number pool fail, the task will be executed in the backup number pool and additional charges will be incurred.", "绘图模式": "Drawing mode", "请选择模式": "Please select mode", "图片代理方式": "Picture agency method", "用于替换 https://cdn.discordapp.com 的域名": "The domain name used to replace https://cdn.discordapp.com", "一个月": "A month", "一天": "One day", "令牌渠道分组选择": "Token channel grouping selection", "只可使用对应分组包含的模型。": "Only models contained in the corresponding group can be used.", "渠道分组": "Channel grouping", "安全设置(可选)": "Security settings (optional)", "IP 限制": "IP restrictions", "模型限制": "Model restrictions", "秒": "Second", "更新令牌后需等待几分钟生效": "It will take a few minutes to take effect after updating the token.", "一小时": "One hour", "新建数量": "New quantity", "未设置": "Not set", "API文档": "API documentation", "不是合法的 JSON 字符串": "Not a valid JSON string", "个人中心": "Personal center", "代理商": "Agent", "钱包": "Wallet", "备注": "Remark", "工作台": "Workbench", "已复制：": "Copied:", "提交时间": "Submission time", "无法正常连接至服务器！": "Unable to connect to the server properly!", "无记录": "No record", "日间模式": "day mode", "活动福利": "Activity benefits", "聊天/绘画": "Chat/Draw", "跟随系统": "Follow the system", "黑夜模式": "Dark mode", "管理员设置": "Admin", "待更新": "To be updated", "定价": "Pricing", "支付中..": "Paying", "查看图片": "View pictures", "并发限制": "Concurrency limit", "正常": "Normal", "周期": "cycle", "同步频率10-20分钟": "Synchronization frequency 10-20 minutes", "模型调用占比": "Model call proportion", "次，平均每天": "times, average per day", "，平均每天": ", on average every day", "启用突发备用号池（建议勾选，极大降低故障率）": "Enable burst backup number pool (it is recommended to check this box to greatly reduce the failure rate)", "查看说明": "View instructions", "添加令牌": "Create token", "IP限制": "IP restrictions", "令牌纬度控制 Midjouney 配置，设置优先级：令牌 > 路径参数 > 系统默认": "Token latitude controls Midjouney configuration, setting priority: token > path parameter > system default", "启用速率限制": "Enable rate limiting", "复制BaseURL": "Copy BaseURL", "总消耗额度": "Total consumption amount", "近一分钟内消耗Token数": "Number of tokens consumed in the past minute", "近一分钟内消耗额度": "<PERSON><PERSON>ta consumed in the past minute", "近一分钟内请求次数": "Number of requests in the past minute", "预估一天消耗量": "Estimated daily consumption", "模型固定价格：": "Model fixed price:", "仅供参考，以实际扣费为准": "For reference only, actual deduction shall prevail", "导出CSV": "Export CSV", "流": "stream", "任务ID": "Task ID", "周": "week", "总计：": "Total:", "划转到余额": "Transfer to balance", "可用额度": "Available credit", "邀请码：": "Invitation code:", "最低": "lowest", "划转额度": "Transfer amount", "邀请链接": "Invitation link", "划转邀请额度": "Transfer invitation quota", "可用邀请额度": "Available invitation quota", "更多优惠": "More offers", "企业微信": "Enterprise WeChat", "点击解绑WxPusher": "Click to unbind <PERSON><PERSON><PERSON><PERSON><PERSON>", "点击显示二维码": "Click to display the QR code", "二维码已过期，点击重新获取": "The QR code has expired, click to get it again", "邮件": "Mail", "个人信息": "Personal information", "余额不足预警": "Insufficient balance warning", "促销活动通知": "Promotion notification", "修改密码、邮箱、微信等": "Change password, email, WeChat, etc.", "更多选项": "More options", "模型调价通知": "Model price adjustment notice", "系统公告通知": "System announcement notification", "订阅管理": "Subscription management", "防失联-定期通知": "Prevent loss of contact - regular notifications", "订阅事件后，当事件触发时，您将会收到相应的通知": "After subscribing to the event, you will receive the corresponding notification when the event is triggered.", "当余额低于 ": "When the balance is lower than", "保存": "save", "计费说明": "Billing instructions", "高稳定性": "High stability", "没有账号请先": "If you don't have an account, please", "注册账号": "Register an account", "第三方登录": "Third party login", "欢迎回来": "welcome back", "忘记密码": "forget the password", "想起来了？": "Remember?", "退出": "Quit", "确定": "OK", "请输入星火大模型版本，注意是接口地址中的版本号，例如：v2[1]": "Please enter the Spark model version, note that it is the version number in the interface address, for example: v2.1", "等待中": "Waiting", "所有各厂聊天模型请统一使用OpenAI方式请求，支持OpenAI官方库": "Please use the OpenAI method to request all chat models from each factory, and support the OpenAI official library.", "实付金额：": "Actual payment amount: ", "金额": "Amount", "充值金额": "Recharge amount", "易支付 实付金额：": "Easy Pay Actual payment amount:", "微信扫码关注公众号，输入 ": "<PERSON>an the QR code on WeChat to follow the official account and enter", " 获取验证码（三分钟内有效）": "Get verification code (valid within three minutes)", "不可用模型": "Unavailable model", "关": "close", "加载首页内容失败": "Failed to load home page content", "打开聊天": "Open chat", "新窗口打开": "New window opens", "禁用（仍可为用户单独开启）": "Disabled (can still be turned on individually for users)", "重新配置": "Reconfigure", "隐藏不可用模型": "Hide unavailable models", " 时，将收到预警通知（2小时最多1次）": "When, you will receive an early warning notification (maximum once every 2 hours)", "在iframe中加载": "Load in iframe", "补全倍率": "Completion ratio", "保存分组数据失败": "Failed to save group data", "保存失败，请重试": "Save failed, please try again", "没有可用的使用信息": "No usage information available", "使用详情": "Usage details", "收起": "Collapse", "计费详情": "Billing details", "提示Token": "<PERSON><PERSON>", "补全Token": "Complete Token", "提示Token详情": "Prompt Token details", "补全Token详情": "Complete Token details", "输出Token详情": "Output Token details", "缓存Token": "CacheToken", "内部缓存Token": "Internal cache token", "图像Token": "ImageToken", "音频Token": "AudioToken", "开": "open", "推理Token": "ReasoningToken", "文本Token": "TextToken", "显示禁用渠道": "Show disabled channels", "输入Token详情": "Enter Token details", "输出Token": "OutputToken", "隐藏禁用渠道": "Hide disabled channels", "今日不再提醒": "No more reminders today", "平台/类型": "Platform/Type", "平台和类型": "Platforms and types", "当前选择分组": "Currently selected group", "表情迁移": "Expression migration", "音频输入：": "Audio input:", "音频输出：": "Audio output:", "风格重绘": "style repaint", "发送测试通知失败": "Failed to send test notification", "开始时间": "start time", "当前所选分组不可用": "The currently selected group is unavailable", "接口凭证": "Interface credentials", "文字输入": "Text input", "文字输出": "text output", "日志详情": "Log details", "未完成": "Not completed", "测试单个渠道操作项目组": "Test a single channel operation project group", "测试通知": "Test notification", "测试通知发送成功": "Test notification sent successfully", "点击此处查看接入文档": "Click here to view access documentation", "类型1": "Type 1", "类型1 (Imagine)": "Type 1 (Imagine)", "类型1价格": "Type 1 price", "类型2": "Type 2", "类型2 (Upscale)": "Type 2 (Upscale)", "类型2价格": "Type 2 price", "类型3价格": "Type 3 price", "计费过程": "Binning process", "语音输入": "Voice input", "语音输出": "Voice output", "请在右侧切换到可用分组": "Please switch to available groups on the right", "请联系管理员~": "Please contact the administrator~", "调用消费": "Call consumption", "质量": "quality", "速度": "speed", "钉钉机器人Key": "DingTalk Robot Key", "需要@的用户手机号": "Need @ user mobile phone number", "（提示": "(hint", "下载文件": "Download file", "https...xxx.com.webhook": "", "搜索渠道的 ID，名称和密钥 ": "", "搜索用户的 ID，用户名，显示名称，以及邮箱地址 ": "", "操作失败，重定向至登录界面中": "", "支付中": "", "等级": "grade", "钉钉": "DingTalk", "模型价格：${{price}} * 分组倍率：{{ratio}} = ${{total}}": "Model price: ${{price}} * Group ratio: {{ratio}} = ${{total}}", "输入：${{price}} * {{ratio}} = ${{total}} / 1M tokens": "Prompt: ${{price}} * {{ratio}} = ${{total}} / 1M tokens", "输出：${{price}} * {{ratio}} = ${{total}} / 1M tokens": "Completion: ${{price}} * {{ratio}} = ${{total}} / 1M tokens", "图片输入：${{price}} * {{ratio}} = ${{total}} / 1M tokens (图片倍率: {{imageRatio}})": "Image input: ${{price}} * {{ratio}} = ${{total}} / 1M tokens (Image ratio: {{imageRatio}})", "音频输入：${{price}} * {{ratio}} * {{audioRatio}} = ${{total}} / 1M tokens": "Audio prompt: ${{price}} * {{ratio}} * {{audioRatio}} = ${{total}} / 1M tokens", "音频提示 {{input}} tokens / 1M tokens * ${{price}} * {{audioRatio}} + 音频补全 {{completion}} tokens / 1M tokens * ${{price}} * {{audioRatio}} * {{audioCompRatio}}": "Audio prompt {{input}} tokens / 1M tokens * ${{price}} * {{audioRatio}} + Audio completion {{completion}} tokens / 1M tokens * ${{price}} * {{audioRatio}} * {{audioCompRatio}}", "音频输出：${{price}} * {{ratio}} * {{audioRatio}} * {{audioCompRatio}} = ${{total}} / 1M tokens": "Audio completion: ${{price}} * {{ratio}} * {{audioRatio}} * {{audioCompRatio}} = ${{total}} / 1M tokens", "输入 {{nonImageInput}} tokens + 图片输入 {{imageInput}} tokens * {{imageRatio}} / 1M tokens * ${{price}} + 输出 {{completion}} tokens / 1M tokens * ${{compPrice}} * 分组 {{ratio}} = ${{total}}": "Input {{nonImageInput}} tokens + Image input {{imageInput}} tokens * {{imageRatio}} / 1M tokens * ${{price}} + Output {{completion}} tokens / 1M tokens * ${{compPrice}} * Group {{ratio}} = ${{total}}", "（文字 + 音频）* 分组倍率 {{ratio}} = ${{total}}": "(Text + Audio) * Group ratio {{ratio}} = ${{total}}", "文字提示 {{input}} tokens / 1M tokens * ${{price}} + 文字补全 {{completion}} tokens / 1M tokens * ${{compPrice}} +": "Text prompt {{input}} tokens / 1M tokens * ${{price}} + Text completion {{completion}} tokens / 1M tokens * ${{compPrice}} +", "输入 {{input}} tokens / 1M tokens * ${{price}} + 输出 {{completion}} tokens / 1M tokens * ${{compPrice}} * 分组 {{ratio}} = ${{total}}": "Prompt {{input}} tokens / 1M tokens * ${{price}} + Completion {{completion}} tokens / 1M tokens * ${{compPrice}} * Group {{ratio}} = ${{total}}", "价格：${{price}} * 分组：{{ratio}}": "Price: ${{price}} * Group: {{ratio}}", "模型: {{ratio}} * 分组: {{groupRatio}}": "Model: {{ratio}} * Group: {{groupRatio}}", "统计额度": "Statistical quota", "统计Tokens": "Statistical Tokens", "统计次数": "Statistical count", "平均RPM": "Average RPM", "平均TPM": "Average TPM", "消耗分布": "Consumption distribution", "调用次数分布": "Models call distribution", "消耗趋势": "Consumption trend", "模型消耗趋势": "Model consumption trend", "调用次数排行": "Models call ranking", "模型调用次数排行": "Model call ranking", "添加渠道": "Add channel", "测试所有通道": "Test all channels", "删除禁用通道": "Delete disabled channels", "修复数据库一致性": "Fix database consistency", "删除所选通道": "Delete selected channels", "标签聚合模式": "Enable tag mode", "没有账户？": "No account? ", "请输入 AZURE_OPENAI_ENDPOINT，例如：https://docs-test-001.openai.azure.com": "Please enter AZURE_OPENAI_ENDPOINT, e.g.: https://docs-test-001.openai.azure.com", "默认 API 版本": "Default API Version", "请输入默认 API 版本，例如：2025-04-01-preview": "Please enter default API version, e.g.: 2025-04-01-preview.", "请为渠道命名": "Please name the channel", "请选择可以使用该渠道的分组": "Please select groups that can use this channel", "请在系统设置页面编辑分组倍率以添加新的分组：": "Please edit Group ratios in system settings to add new groups:", "部署地区": "Deployment Region", "请输入部署地区，例如：us-central1\n支持使用模型映射格式": "Please enter deployment region, e.g.: us-central1\nSupports model mapping format", "填入模板": "Fill Template", "鉴权json": "Authentication JSON", "请输入鉴权json": "Please enter authentication JSON", "组织": "Organization", "组织，不填则为默认组织": "Organization, default if empty", "请输入组织org-xxx": "Please enter organization org-xxx", "默认测试模型": "Default Test Model", "不填则为模型列表第一个": "First model in list if empty", "是否自动禁用（仅当自动禁用开启时有效），关闭后不会自动禁用该渠道": "Auto-disable (only effective when auto-disable is enabled). When turned off, this channel will not be automatically disabled", "状态码复写": "Status Code Override", "此项可选，用于复写返回的状态码，仅影响本地判断，不修改返回到上游的状态码，比如将claude渠道的400错误复写为500（用于重试），请勿滥用该功能，例如：": "Optional, used to override returned status codes, only affects local judgment, does not modify status code returned upstream, e.g. rewriting Claude channel's 400 error to 500 (for retry). Do not abuse this feature. Example:", "渠道标签": "Channel Tag", "渠道优先级": "Channel Priority", "渠道权重": "Channel Weight", "仅支持 OpenAI 接口格式": "Only OpenAI interface format is supported", "请填写密钥": "Please enter the key", "获取模型列表成功": "Successfully retrieved model list", "获取模型列表失败": "Failed to retrieve model list", "请填写渠道名称和渠道密钥！": "Please enter channel name and key!", "请至少选择一个模型！": "Please select at least one model!", "提交失败，请勿重复提交！": "Submission failed, please do not submit repeatedly!", "某些模型已存在！": "Some models already exist!", "如果你对接的是上游One API或者New API等转发项目，请使用OpenAI类型，不要使用此类型，除非你知道你在做什么。": "If you are connecting to upstream One API or New API forwarding projects, please use OpenAI type. Do not use this type unless you know what you are doing.", "完整的 Base URL，支持变量{model}": "Complete Base URL, supports variable {model}", "请输入完整的URL，例如：https://api.openai.com/v1/chat/completions": "Please enter complete URL, e.g.: https://api.openai.com/v1/chat/completions", "此项可选，用于通过自定义API地址来进行 API 调用，末尾不要带/v1和/": "Optional for API calls through custom API address, do not add /v1 and / at the end", "私有部署地址": "Private Deployment Address", "请输入私有部署地址，格式为：https://fastgpt.run/api/openapi": "Please enter private deployment address, format: https://fastgpt.run/api/openapi", "注意非Chat API，请务必填写正确的API地址，否则可能导致无法使用": "Note: For non-Chat API, please make sure to enter the correct API address, otherwise it may not work", "请输入到 /suno 前的路径，通常就是域名，例如：https://api.example.com": "Please enter the path before /suno, usually the domain, e.g.: https://api.example.com", "填入相关模型": "Fill Related Models", "新建渠道时，请求通过当前浏览器发出；编辑已有渠道，请求通过后端服务器发出": "When creating a new channel, requests are sent through the current browser; when editing an existing channel, requests are sent through the backend server", "获取模型列表": "Get Model List", "填入": "Fill", "输入自定义模型名称": "Enter Custom Model Name", "知识库 ID": "Knowledge Base ID", "请输入知识库 ID，例如：123456": "Please enter knowledge base ID, e.g.: 123456", "可选值": "Optional value", "任务日志": "Task log", "你好": "Hello", "你好，请问有什么可以帮助您的吗？": "Hello, how may I help you?", "用户分组": "Your default group", "每页条数": "Items per page", "令牌无法精确控制使用额度，只允许自用，请勿直接将令牌分发给他人。": "Tokens cannot accurately control usage, only for self-use, please do not distribute tokens directly to others.", "添加兑换码": "Add redemption code", "复制所选兑换码到剪贴板": "Copy selected redemption codes to clipboard", "第 {{start}} - {{end}} 条，共 {{total}} 条": "Items {{start}} - {{end}} of {{total}}", "新建兑换码": "Code", "兑换码更新成功！": "Redemption code updated successfully!", "兑换码创建成功！": "Redemption code created successfully!", "兑换码创建成功": "Redemption Code Created", "兑换码创建成功，是否下载兑换码？": "Redemption code created successfully. Do you want to download it?", "兑换码将以文本文件的形式下载，文件名为兑换码的名称。": "The redemption code will be downloaded as a text file, with the filename being the redemption code name.", "模型价格": "Model price", "可用分组": "Available groups", "您的默认分组为：{{group}}，分组倍率为：{{ratio}}": "Your default group is: {{group}}, group ratio: {{ratio}}", "按量计费费用 = 分组倍率 × 模型倍率 × （提示token数 + 补全token数 × 补全倍率）/ 500000 （单位：美元）": "The cost of pay-as-you-go = Group ratio × Model ratio × (Prompt token number + Completion token number × Completion ratio) / 500000 (Unit: USD)", "模糊搜索模型名称": "Fuzzy search model name", "您还未登陆，显示的价格为默认分组倍率: {{ratio}}": "You are not logged in, the displayed price is the default group ratio: {{ratio}}", "你的分组无权使用该模型": "Your group is not authorized to use this model", "您的分组可以使用该模型": "Your group can use this model", "当前查看的分组为：{{group}}，倍率为：{{ratio}}": "Current group: {{group}}, ratio: {{ratio}}", "添加用户": "Add user", "角色": "Role", "已绑定的 Telegram 账户": "Bound Telegram account", "新额度：": "New quota: ", "需要添加的额度（支持负数）": "Need to add quota (supports negative numbers)", "此项只读，需要用户通过个人设置页面的相关绑定按钮进行绑定，不可直接修改": "Read-only, user's personal settings, and cannot be modified directly", "请输入新的密码，最短 8 位": "Please enter a new password, at least 8 characterss", "添加额度": "Add quota", "以下信息不可修改": "The following information cannot be modified", "充值确认": "Recharge confirmation", "充值数量": "Recharge quantity", "实付金额": "Actual payment amount", "是否确认充值？": "Confirm recharge?", "我的钱包": "My wallet", "默认聊天页面链接": "Default chat page link", "聊天页面 2 链接": "Chat page 2 link", "失败重试次数": "Failed retry times", "额度查询接口返回令牌额度而非用户额度": "Displays token quota instead of user quota", "默认折叠侧边栏": "De<PERSON>ult collapse sidebar", "聊天链接功能已经弃用，请使用下方聊天设置功能": "Chat link function has been deprecated, please use the chat settings below", "你似乎并没有修改什么": "You seem to have not modified anything", "聊天设置": "Chat settings", "必须将上方聊天链接全部设置为空，才能使用下方聊天设置功能": "Must set all chat links above to empty to use the chat settings below", "链接中的{key}将自动替换为sk-xxxx，{address}将自动替换为系统设置的服务器地址，末尾不带/和/v1": "The {key} in the link will be automatically replaced with sk-xxxx, the {address} will be automatically replaced with the server address in system settings, and the end will not have / and /v1", "聊天配置": "Chat configuration", "保存聊天设置": "Save chat settings", "绘图设置": "Drawing settings", "启用绘图功能": "Enable drawing function", "允许回调（会泄露服务器 IP 地址）": "Allow callback (will leak server IP address)", "允许 AccountFilter 参数": "Allow AccountFilter parameter", "开启之后将上游地址替换为服务器地址": "After enabling, the upstream address will be replaced with the server address", "开启之后会清除用户提示词中的": "After enabling, the user prompt will be cleared", "检测必须等待绘图成功才能进行放大等操作": "Detection must wait for drawing to succeed before performing zooming and other operations", "保存绘图设置": "Save drawing settings", "以及": "and", "参数": "parameter", "屏蔽词过滤设置": "Sensitive word filtering settings", "启用屏蔽词过滤功能": "Enable sensitive word filtering function", "启用 Prompt 检查": "Enable Prompt check", "屏蔽词列表": "Sensitive word list", "一行一个屏蔽词，不需要符号分割": "One line per sensitive word, no symbols are required", "保存屏蔽词过滤设置": "Save sensitive word filtering settings", "日志设置": "Log settings", "日志记录时间": "Log record time", "请选择日志记录时间": "Please select log record time", "清除历史日志": "Clear historical logs", "条日志已清理！": "logs have been cleared!", "保存日志设置": "Save log settings", "数据看板设置": "Data dashboard settings", "启用数据看板（实验性）": "Enable data dashboard (experimental)", "数据看板更新间隔": "Data dashboard update interval", "数据看板默认时间粒度": "Data dashboard default time granularity", "保存数据看板设置": "Save data dashboard settings", "请选择最长响应时间": "Please select longest response time", "成功时自动启用通道": "Enable channel when successful", "分钟": "minutes", "设置过短会影响数据库性能": "Setting too short will affect database performance", "仅修改展示粒度，统计精确到小时": "Only modify display granularity, statistics accurate to the hour", "当运行通道全部测试时，超过此时间将自动禁用通道": "When running all channel tests, the channel will be automatically disabled when this time is exceeded", "设置公告": "Set notice", "设置 Logo": "Set <PERSON>", "设置首页内容": "Set home page content", "设置关于": "Set about", "公告已更新": "Notice updated", "系统名称已更新": "System name updated", "Logo 已更新": "Logo updated", "首页内容已更新": "Home page content updated", "关于已更新": "About updated", "模型测试": "model test", "当前未开启Midjourney回调，部分项目可能无法获得绘图结果，可在运营设置中开启。": "Current Midjourney callback is not enabled, some projects may not be able to obtain drawing results, which can be enabled in the operation settings.", "Telegram 身份验证": "Telegram authentication", "Linux DO 身份验证": "Linux DO authentication", "协议": "License", "修改子渠道权重": "Modify sub-channel weight", "确定要修改所有子渠道权重为 ": "Confirm to modify all sub-channel weights to ", " 吗？": "?", "修改子渠道优先级": "Modify sub-channel priority", "确定要修改所有子渠道优先级为 ": "Confirm to modify all sub-channel priorities to ", "分组倍率设置": "Group ratio settings", "用户可选分组": "User selectable groups", "保存分组倍率设置": "Save group ratio settings", "模型倍率设置": "Model ratio settings", "可视化倍率设置": "Visual model ratio settings", "确定重置模型倍率吗？": "Confirm to reset model ratio?", "模型固定价格": "Model price per call", "模型补全倍率（仅对自定义模型有效）": "Model completion ratio (only effective for custom models)", "保存模型倍率设置": "Save model ratio settings", "重置模型倍率": "Reset model ratio", "一次调用消耗多少刀，优先级大于模型倍率": "How much USD one call costs, priority over model ratio", "仅对自定义模型有效": "Only effective for custom models", "添加模型": "Add model", "应用更改": "Apply changes", "更多": "Expand more", "个模型": "models", "可用模型": "Available models", "时间范围": "Time range", "批量设置标签": "Batch set tag", "请输入要设置的标签名称": "Please enter the tag name to be set", "请输入标签名称": "Please enter the tag name", "支持搜索用户的 ID、用户名、显示名称和邮箱地址": "Support searching for user ID, username, display name, and email address", "已注销": "Logged out", "自动禁用关键词": "Automatic disable keywords", "一行一个，不区分大小写": "One line per keyword, not case-sensitive", "当上游通道返回错误中包含这些关键词时（不区分大小写），自动禁用通道": "When the upstream channel returns an error containing these keywords (not case-sensitive), automatically disable the channel", "请求并计费模型": "Request and charge model", "实际模型": "Actual model", "渠道信息": "Channel information", "通知设置": "Notification settings", "Webhook地址": "Webhook URL", "请输入Webhook地址，例如: https://example.com/webhook": "Please enter the Webhook URL, e.g.: https://example.com/webhook", "邮件通知": "Email notification", "Webhook通知": "Webhook notification", "接口凭证（可选）": "Interface credentials (optional)", "密钥将以 Bearer 方式添加到请求头中，用于验证webhook请求的合法性": "The secret will be added to the request header as a Bear<PERSON> token to verify the legitimacy of the webhook request", "Authorization: Bearer your-secret-key": "Authorization: Bearer your-secret-key", "额度预警阈值": "Quota warning threshold", "当剩余额度低于此数值时，系统将通过选择的方式发送通知": "When the remaining quota is lower than this value, the system will send a notification through the selected method", "Webhook请求结构": "Webhook request structure", "只支持https，系统将以 POST 方式发送通知，请确保地址可以接收 POST 请求": "Only https is supported, the system will send a notification through POST, please ensure the address can receive POST requests", "保存设置": "Save settings", "通知邮箱": "Notification email", "设置用于接收额度预警的邮箱地址，不填则使用账号绑定的邮箱": "Set the email address for receiving quota warning notifications, if not set, the email address bound to the account will be used", "留空则使用账号绑定的邮箱": "If left blank, the email address bound to the account will be used", "API地址": "Base URL", "对于官方渠道，new-api已经内置地址，除非是第三方代理站点或者Azure的特殊接入地址，否则不需要填写": "For official channels, the new-api has a built-in address. Unless it is a third-party proxy site or a special Azure access address, there is no need to fill it in", "渠道额外设置": "Channel extra settings", "参数覆盖": "Parameters override", "模型请求速率限制": "Model request rate limit", "启用用户模型请求速率限制（可能会影响高并发性能）": "Enable user model request rate limit (may affect high concurrency performance)", "限制周期": "Limit period", "用户每周期最多请求次数": "User max request times per period", "用户每周期最多请求完成次数": "User max successful request times per period", "包括失败请求的次数，0代表不限制": "Including failed request times, 0 means no limit", "频率限制的周期（分钟）": "Rate limit period (minutes)", "只包括请求成功的次数": "Only include successful request times", "保存模型速率限制": "Save model rate limit settings", "速率限制设置": "Rate limit settings", "获取启用模型失败:": "Failed to get enabled models:", "获取启用模型失败": "Failed to get enabled models", "JSON解析错误:": "JSON parsing error:", "保存失败:": "Save failed:", "输入模型倍率": "Enter model ratio", "输入补全倍率": "Enter completion ratio", "请输入数字": "Please enter a number", "模型名称已存在": "Model name already exists", "添加成功": "Added successfully", "请先选择需要批量设置的模型": "Please select models for batch setting first", "请输入模型倍率和补全倍率": "Please enter model ratio and completion ratio", "请输入有效的数字": "Please enter a valid number", "请输入填充值": "Please enter a value", "批量设置成功": "Batch setting successful", "已为 {{count}} 个模型设置{{type}}": "Set {{type}} for {{count}} models", "固定价格": "Fixed Price", "模型倍率和补全倍率": "Model Ratio and Completion Ratio", "批量设置": "<PERSON><PERSON> Setting", "搜索模型名称": "Search model name", "此页面仅显示未设置价格或倍率的模型，设置后将自动从列表中移除": "This page only shows models without price or ratio settings. After setting, they will be automatically removed from the list", "没有未设置的模型": "No unconfigured models", "定价模式": "Pricing Mode", "固定价格(每次)": "Fixed Price (per use)", "输入每次价格": "Enter per-use price", "批量设置模型参数": "Batch Set Model Parameters", "设置类型": "Setting Type", "模型倍率值": "Model Ratio Value", "补全倍率值": "Completion Ratio Value", "请输入模型倍率": "Enter model ratio", "请输入补全倍率": "Enter completion ratio", "请输入数值": "Enter a value", "将为选中的 ": "Will set for selected ", " 个模型设置相同的值": " models with the same value", "当前设置类型: ": "Current setting type: ", "固定价格值": "Fixed Price Value", "未设置倍率模型": "Models without ratio settings", "模型倍率和补全倍率同时设置": "Both model ratio and completion ratio are set", "自用模式": "Self-use mode", "开启后不限制：必须设置模型倍率": "After enabling, no limit: must set model ratio", "演示站点模式": "Demo site mode", "当前版本": "Current version", "Gemini设置": "Gemini settings", "Gemini安全设置": "Gemini safety settings", "default为默认设置，可单独设置每个分类的安全等级": "\"default\" is the default setting, and each category can be set separately", "Gemini版本设置": "Gemini version settings", "default为默认设置，可单独设置每个模型的版本": "\"default\" is the default setting, and each model can be set separately", "Claude设置": "Claude settings", "Claude请求头覆盖": "Claude request header override", "示例": "Example", "缺省 MaxTokens": "De<PERSON>ult <PERSON>", "启用Claude思考适配（-thinking后缀）": "Enable Claude thinking adaptation (-thinking suffix)", "和Claude不同，默认情况下Gemini的思考模型会自动决定要不要思考，就算不开启适配模型也可以正常使用，": "Unlike <PERSON>, Gemini's thinking model automatically decides whether to think by default, and can be used normally even without enabling the adaptation model.", "如果您需要计费，推荐设置无后缀模型价格按思考价格设置。": "If you need billing, it is recommended to set the no-suffix model price according to the thinking price.", "支持使用 gemini-2.5-pro-preview-06-05-thinking-128 格式来精确传递思考预算。": "Supports using gemini-2.5-pro-preview-06-05-thinking-128 format to precisely pass thinking budget.", "启用Gemini思考后缀适配": "Enable Gemini thinking suffix adaptation", "适配-thinking、-thinking-预算数字和-nothinking后缀": "Adapt -thinking, -thinking-budgetNumber, and -nothinking suffixes", "思考预算占比": "Thinking budget ratio", "Claude思考适配 BudgetTokens = MaxTokens * BudgetTokens 百分比": "Claude thinking adaptation BudgetTokens = MaxTokens * BudgetTokens percentage", "思考适配 BudgetTokens 百分比": "Thinking adaptation BudgetTokens percentage", "0.1-1之间的小数": "Decimal between 0.1 and 1", "模型相关设置": "Model related settings", "收起侧边栏": "Collapse sidebar", "展开侧边栏": "Expand sidebar", "提示缓存倍率": "Prompt cache ratio", "缓存：${{price}} * {{ratio}} = ${{total}} / 1M tokens (缓存倍率: {{cacheRatio}})": "Cache: ${{price}} * {{ratio}} = ${{total}} / 1M tokens (cache ratio: {{cacheRatio}})", "提示 {{nonCacheInput}} tokens + 缓存 {{cacheInput}} tokens * {{cacheRatio}} / 1M tokens * ${{price}} + 补全 {{completion}} tokens / 1M tokens * ${{compPrice}} * 分组 {{ratio}} = ${{total}}": "Prompt {{nonCacheInput}} tokens + cache {{cacheInput}} tokens * {{cacheRatio}} / 1M tokens * ${{price}} + completion {{completion}} tokens / 1M tokens * ${{compPrice}} * group {{ratio}} = ${{total}}", "缓存 Tokens": "<PERSON><PERSON>", "系统初始化": "System initialization", "管理员账号已经初始化过，请继续设置其他参数": "The admin account has already been initialized, please continue to set other parameters", "管理员账号": "Admin account", "请输入管理员用户名": "Please enter the admin username", "请输入管理员密码": "Please enter the admin password", "请确认管理员密码": "Please confirm the admin password", "请选择使用模式": "Please select the usage mode", "数据库警告": "Database warning", "您正在使用 SQLite 数据库。如果您在容器环境中运行，请确保已正确设置数据库文件的持久化映射，否则容器重启后所有数据将丢失！": "You are using the SQLite database. If you are running in a container environment, please ensure that the database file persistence mapping is correctly set, otherwise all data will be lost after container restart!", "建议在生产环境中使用 MySQL 或 PostgreSQL 数据库，或确保 SQLite 数据库文件已映射到宿主机的持久化存储。": "It is recommended to use MySQL or PostgreSQL databases in production environments, or ensure that the SQLite database file is mapped to the persistent storage of the host machine.", "使用模式": "Usage mode", "对外运营模式": "Default mode", "密码长度至少为8个字符": "Password must be at least 8 characters long", "表单引用错误，请刷新页面重试": "Form reference error, please refresh the page and try again", "默认模式，适用于为多个用户提供服务的场景。": "Default mode, suitable for scenarios where multiple users are provided.", "此模式下，系统将计算每次调用的用量，您需要对每个模型都设置价格，如果没有设置价格，用户将无法使用该模型。": "In this mode, the system will calculate the usage of each call, you need to set the price for each model, if the price is not set, the user will not be able to use the model.", "适用于个人使用的场景。": "Suitable for personal use.", "不需要设置模型价格，系统将弱化用量计算，您可专注于使用模型。": "No need to set the model price, the system will weaken the usage calculation, you can focus on using the model.", "适用于展示系统功能的场景。": "Suitable for scenarios where the system functions are displayed.", "可在初始化后修改": "Can be modified after initialization", "初始化系统": "Initialize system", "支持众多的大模型供应商": "Supporting various LLM providers", "统一的大模型接口网关": "The Unified LLMs API Gateway", "更好的价格，更好的稳定性，只需要将模型基址替换为：": "Better price, better stability, no subscription required, just replace the model BASE URL with: ", "获取密钥": "Get Key", "关于我们": "About Us", "关于项目": "About Project", "联系我们": "Contact Us", "功能特性": "Features", "快速开始": "Quick Start", "安装指南": "Installation Guide", "API 文档": "API Documentation", "相关项目": "Related Projects", "基于New API的项目": "Projects Based on New API", "版权所有": "All rights reserved", "设计与开发由": "Designed & Developed with love by", "演示站点": "Demo Site", "页面未找到，请检查您的浏览器地址是否正确": "Page not found, please check if your browser address is correct", "New API项目仓库地址：": "New API project repository address: ", "© {{currentYear}}": "© {{currentYear}}", "| 基于": " | Based on ", "MIT许可证": "MIT License", "Apache-2.0协议": "Apache-2.0 License", "本项目根据": "This project is licensed under the ", "授权，需在遵守": " and must be used in compliance with the ", "的前提下使用。": ".", "管理员暂时未设置任何关于内容": "The administrator has not set any custom About content yet", "早上好": "Good morning", "中午好": "Good afternoon", "下午好": "Good afternoon", "晚上好": "Good evening", "更多提示信息": "More Prompts", "新建": "Create", "更新": "Update", "基本信息": "Basic Information", "设置令牌的基本信息": "Set token basic information", "设置令牌可用额度和数量": "Set token available quota and quantity", "访问限制": "Access Restrictions", "设置令牌的访问限制": "Set token access restrictions", "请勿过度信任此功能，IP可能被伪造": "Do not over-trust this feature, IP can be spoofed", "模型限制列表": "Model restrictions list", "请选择该令牌支持的模型，留空支持所有模型": "Select models supported by the token, leave blank to support all models", "非必要，不建议启用模型限制": "Not necessary, model restrictions are not recommended", "分组信息": "Group Information", "设置令牌的分组": "Set token grouping", "管理员未设置用户可选分组": "Administrator has not set user-selectable groups", "10个": "10 items", "20个": "20 items", "30个": "30 items", "100个": "100 items", "Midjourney 任务记录": "Midjourney Task Records", "任务记录": "Task Records", "兑换码可以批量生成和分发，适合用于推广活动或批量充值。": "Redemption codes can be batch generated and distributed, suitable for promotion activities or bulk recharge.", "剩余": "Remaining", "已用": "Used", "调用": "Calls", "邀请": "Invitations", "收益": "Earnings", "无邀请人": "No Inviter", "邀请人": "Inviter", "用户管理页面，可以查看和管理所有注册用户的信息、权限和状态。": "User management page, you can view and manage all registered user information, permissions, and status.", "设置兑换码的基本信息": "Set redemption code basic information", "设置兑换码的额度和数量": "Set redemption code quota and quantity", "编辑用户": "Edit User", "权限设置": "Permission Settings", "用户的基本账户信息": "User basic account information", "用户分组和额度管理": "User Group and Quota Management", "绑定信息": "Binding Information", "第三方账户绑定状态（只读）": "Third-party account binding status (read-only)", "已绑定的 OIDC 账户": "Bound OIDC accounts", "使用兑换码充值余额": "Recharge balance with redemption code", "支持多种支付方式": "Support multiple payment methods", "尊敬的": "Dear", "请输入兑换码": "Please enter the redemption code", "在线充值功能未开启": "Online recharge function is not enabled", "管理员未开启在线充值功能，请联系管理员开启或使用兑换码充值。": "The administrator has not enabled the online recharge function, please contact the administrator to enable it or recharge with a redemption code.", "点击模型名称可复制": "Click the model name to copy", "管理您的邀请链接和收益": "Manage your invitation link and earnings", "没有可用模型": "No available models", "账户绑定": "Account Binding", "安全设置": "Security Settings", "系统访问令牌": "System Access Token", "用于API调用的身份验证令牌，请妥善保管": "Authentication token for API calls, please keep it safe", "密码管理": "Password Management", "定期更改密码可以提高账户安全性": "Regularly changing your password can improve account security", "删除账户": "Delete Account", "此操作不可逆，所有数据将被永久删除": "This operation is irreversible, all data will be permanently deleted", "生成令牌": "Generate Token", "通过邮件接收通知": "Receive notifications via email", "通过HTTP请求接收通知": "Receive notifications via HTTP request", "价格设置": "Price Settings", "重新生成": "Regenerate", "绑定微信账户": "Bind <PERSON><PERSON><PERSON> Account", "原密码": "Original Password", "请输入原密码": "Please enter the original password", "请输入新密码": "Please enter the new password", "请再次输入新密码": "Please enter the new password again", "删除账户确认": "Delete Account Confirmation", "请输入您的用户名以确认删除": "Please enter your username to confirm deletion", "接受未设置价格模型": "Accept models without price settings", "当模型没有设置价格时仍接受调用，仅当您信任该网站时使用，可能会产生高额费用": "Accept calls even if the model has no price settings, use only when you trust the website, which may incur high costs", "批量操作": "Batch Operations", "未开始": "Not Started", "测试中": "Testing", "请求时长: ${time}s": "Request time: ${time}s", "搜索模型...": "Search models...", "批量测试${count}个模型": "Batch test ${count} models", "测试中...": "Testing...", "渠道的模型测试": "Channel Model Test", "共": "Total", "确定要测试所有通道吗？": "Are you sure you want to test all channels?", "确定要更新所有已启用通道余额吗？": "Are you sure you want to update the balance of all enabled channels?", "已选择 ${count} 个渠道": "Selected ${count} channels", "渠道的基本配置信息": "Channel basic configuration information", "API 配置": "API Configuration", "API 地址和相关配置": "API URL and related configuration", "模型配置": "Model Configuration", "模型选择和映射设置": "Model selection and mapping settings", "高级设置": "Advanced Settings", "渠道的高级配置选项": "Advanced channel configuration options", "设置说明": "Setting Description", "此项可选，用于配置渠道特定设置，为一个 JSON 字符串，例如：": "This is optional, used to configure channel-specific settings, as a JSON string, for example:", "此项可选，用于覆盖请求参数。不支持覆盖 stream 参数。为一个 JSON 字符串，例如：": "This is optional, used to override request parameters. Does not support overriding the stream parameter. As a JSON string, for example:", "编辑标签": "Edit Tag", "标签信息": "Tag Information", "标签的基本配置": "Tag basic configuration", "所有编辑均为覆盖操作，留空则不更改": "All edits are overwrite operations, leaving blank will not change", "标签名称": "Tag Name", "请选择该渠道所支持的模型，留空则不更改": "Please select the models supported by the channel, leaving blank will not change", "此项可选，用于修改请求体中的模型名称，为一个 JSON 字符串，键为请求中模型名称，值为要替换的模型名称，留空则不更改": "This is optional, used to modify the model name in the request body, as a JSON string, the key is the model name in the request, the value is the model name to be replaced, leaving blank will not change", "清空重定向": "Clear redirect", "不更改": "Not change", "用户分组配置": "User group configuration", "请选择可以使用该渠道的分组，留空则不更改": "Please select the groups that can use this channel, leaving blank will not change", "启用全部": "Enable all", "禁用全部": "Disable all", "模型定价": "Model Pricing", "当前分组": "Current group", "全部模型": "All Models", "智谱": "Zhipu AI", "通义千问": "<PERSON><PERSON>", "文心一言": "ERNIE <PERSON>", "讯飞星火": "Spark Desk", "腾讯混元": "Hunyuan", "360智脑": "360 AI Brain", "零一万物": "<PERSON>", "豆包": "Do<PERSON><PERSON>", "系统公告": "System Notice", "今日关闭": "Close Today", "关闭公告": "Close Notice", "搜索条件": "Search Conditions", "加载中...": "Loading...", "正在跳转...": "Redirecting...", "暂无公告": "No Notice", "操练场": "Playground", "欢迎使用，请完成以下设置以开始使用系统": "Welcome to use, please complete the following settings to start using the system", "数据库信息": "Database Information", "您正在使用 MySQL 数据库。MySQL 是一个可靠的关系型数据库管理系统，适合生产环境使用。": "You are using the MySQL database. MySQL is a reliable relational database management system, suitable for production environments.", "您正在使用 PostgreSQL 数据库。PostgreSQL 是一个功能强大的开源关系型数据库系统，提供了出色的可靠性和数据完整性，适合生产环境使用。": "You are using the PostgreSQL database. PostgreSQL is a powerful open-source relational database system that provides excellent reliability and data integrity, suitable for production environments.", "设置系统管理员的登录信息": "Set the login information for the system administrator", "选择适合您使用场景的模式": "Select the mode suitable for your usage scenario", "使用模式说明": "Usage mode description", "计费模式": "Billing mode", "多用户支持": "Multi-user support", "个人使用": "Personal use", "功能演示": "Function demonstration", "体验试用": "Experience trial", "默认模式": "Default Mode", "无需计费": "No Charge", "演示体验": "Demo Experience", "提供基础功能演示，方便用户了解系统特性。": "Provide basic feature demonstrations to help users understand the system features.", "适用于为多个用户提供服务的场景": "Suitable for scenarios where multiple users are provided.", "适用于个人使用的场景，不需要设置模型价格": "Suitable for personal use, no need to set model price.", "适用于展示系统功能的场景，提供基础功能演示": "Suitable for scenarios where the system functions are displayed, providing basic feature demonstrations.", "账户数据": "Account Data", "使用统计": "Usage Statistics", "资源消耗": "Resource Consumption", "性能指标": "Performance Indicators", "模型数据分析": "Model Data Analysis", "搜索无结果": "No results found", "仪表盘设置": "Dashboard Settings", "API信息管理，可以配置多个API地址用于状态展示和负载均衡（最多50个）": "API information management, you can configure multiple API addresses for status display and load balancing (maximum 50)", "线路描述": "Route description", "颜色": "Color", "标识颜色": "Identifier color", "添加API": "Add API", "API信息": "API Information", "暂无API信息": "No API information", "请输入API地址": "Please enter the API address", "请输入线路描述": "Please enter the route description", "如：大带宽批量分析图片推荐": "e.g. Large bandwidth batch analysis of image recommendations", "请输入说明": "Please enter the description", "如：香港线路": "e.g. Hong Kong line", "请联系管理员在系统设置中配置API信息": "Please contact the administrator to configure API information in the system settings.", "请联系管理员在系统设置中配置公告信息": "Please contact the administrator to configure notice information in the system settings.", "请联系管理员在系统设置中配置常见问答": "Please contact the administrator to configure FAQ information in the system settings.", "请联系管理员在系统设置中配置Uptime": "Please contact the administrator to configure Uptime in the system settings.", "确定要删除此API信息吗？": "Are you sure you want to delete this API information?", "测速": "Speed Test", "跳转": "Jump", "批量删除": "<PERSON><PERSON> Delete", "常见问答": "FAQ", "进行中": "Ongoing", "警告": "Warning", "添加公告": "Add Notice", "编辑公告": "Edit Notice", "公告内容": "Notice Content", "请输入公告内容": "Please enter the notice content", "请输入公告内容（支持 Markdown/HTML）": "Please enter the notice content (supports Markdown/HTML)", "发布日期": "Publish Date", "请选择发布日期": "Please select the publish date", "发布时间": "Publish Time", "公告类型": "Notice Type", "说明信息": "Description", "可选，公告的补充说明": "Optional, additional information for the notice", "确定要删除此公告吗？": "Are you sure you want to delete this notice?", "系统公告管理，可以发布系统通知和重要消息": "System notice management, you can publish system notices and important messages", "暂无系统公告": "No system notice", "添加问答": "Add FAQ", "编辑问答": "Edit FAQ", "问题标题": "Question Title", "请输入问题标题": "Please enter the question title", "回答内容": "Answer Content", "请输入回答内容": "Please enter the answer content", "请输入回答内容（支持 Markdown/HTML）": "Please enter the answer content (supports Markdown/HTML)", "确定要删除此问答吗？": "Are you sure you want to delete this FAQ?", "系统公告管理，可以发布系统通知和重要消息（最多100个，前端显示最新20条）": "System notice management, you can publish system notices and important messages (maximum 100, display latest 20 on the front end)", "常见问答管理，为用户提供常见问题的答案（最多50个，前端显示最新20条）": "FAQ management, providing answers to common questions for users (maximum 50, display latest 20 on the front end)", "暂无常见问答": "No FAQ", "显示最新20条": "Display latest 20", "Uptime Kuma监控分类管理，可以配置多个监控分类用于服务状态展示（最多20个）": "Uptime Kuma monitoring category management, you can configure multiple monitoring categories for service status display (maximum 20)", "添加分类": "Add Category", "分类名称": "Category Name", "Uptime Kuma地址": "Uptime <PERSON><PERSON> Address", "状态页面Slug": "Status Page Slug", "请输入分类名称，如：OpenAI、Claude等": "Please enter the category name, such as: <PERSON><PERSON><PERSON>, <PERSON>, etc.", "请输入Uptime Kuma服务地址，如：https://status.example.com": "Please enter the Uptime Kuma service address, such as: https://status.example.com", "请输入状态页面的Slug，如：my-status": "Please enter the slug for the status page, such as: my-status", "确定要删除此分类吗？": "Are you sure you want to delete this category?", "配置": "Configure", "服务监控地址，用于展示服务状态信息": "service monitoring address for displaying status information", "服务可用性": "Service Status", "可用率": "Availability", "有异常": "Abnormal", "高延迟": "High latency", "维护中": "Maintenance", "暂无监控数据": "No monitoring data", "IP记录": "IP Record", "记录请求与错误日志 IP": "Record request and error log IP", "开启后，仅“消费”和“错误”日志将记录您的客户端 IP 地址": "After enabling, only \"consumption\" and \"error\" logs will record your client IP address", "只有当用户设置开启IP记录时，才会进行请求和错误类型日志的IP记录": "Only when the user sets IP recording, the IP recording of request and error type logs will be performed", "设置保存成功": "Setting<PERSON> saved successfully", "设置保存失败": "Settings save failed", "已新增 {{count}} 个模型：{{list}}": "Added {{count}} models: {{list}}", "未发现新增模型": "No new models were added", "令牌用于API访问认证，可以设置额度限制和模型权限。": "Tokens are used for API access authentication, and can set quota limits and model permissions.", "清除失效兑换码": "Clear invalid redemption codes", "确定清除所有失效兑换码？": "Are you sure you want to clear all invalid redemption codes?", "将删除已使用、已禁用及过期的兑换码，此操作不可撤销。": "This will delete all used, disabled, and expired redemption codes, this operation cannot be undone.", "选择过期时间（可选，留空为永久）": "Select expiration time (optional, leave blank for permanent)", "请输入备注（仅管理员可见）": "Please enter a remark (only visible to administrators)", "上游倍率同步": "Upstream ratio synchronization", "获取渠道失败：": "Failed to get channels: ", "请至少选择一个渠道": "Please select at least one channel", "获取倍率失败：": "Failed to get ratios: ", "后端请求失败": "Backend request failed", "部分渠道测试失败：": "Some channels failed to test: ", "未找到差异化倍率，无需同步": "No differential ratio found, no synchronization is required", "请求后端接口失败：": "Failed to request the backend interface: ", "同步成功": "Synchronization successful", "部分保存失败": "Some settings failed to save", "保存失败": "Save failed", "选择同步渠道": "Select synchronization channel", "应用同步": "Apply synchronization", "倍率类型": "Ratio type", "当前值": "Current value", "上游值": "Upstream value", "差异": "Difference", "搜索渠道名称或地址": "Search channel name or address", "缓存倍率": "Cache ratio", "暂无差异化倍率显示": "No differential ratio display", "请先选择同步渠道": "Please select the synchronization channel first", "与本地相同": "Same as local", "未找到匹配的模型": "No matching model found", "暴露倍率接口": "Expose ratio API", "支付设置": "Payment Settings", "（当前仅支持易支付接口，默认使用上方服务器地址作为回调地址！）": "(Currently only supports Epay interface, the default callback address is the server address above!)", "支付地址": "Payment address", "易支付商户ID": "Epay merchant ID", "易支付商户密钥": "Epay merchant key", "回调地址": "Callback address", "充值价格（x元/美金）": "Recharge price (x yuan/dollar)", "最低充值美元数量": "Minimum recharge dollar amount", "充值分组倍率": "Recharge group ratio", "充值方式设置": "Recharge method settings", "更新支付设置": "Update payment settings", "通知": "Notice", "源地址": "Source address", "同步接口": "Synchronization interface", "置信度": "Confidence", "谨慎": "C<PERSON><PERSON>", "该数据可能不可信，请谨慎使用": "This data may not be reliable, please use with caution", "可信": "Reliable", "所有上游数据均可信": "All upstream data is reliable", "以下上游数据可能不可信：": "The following upstream data may not be reliable: ", "按倍率类型筛选": "Filter by ratio type", "内容": "Content", "放大编辑": "Expand editor", "编辑公告内容": "Edit announcement content", "自适应列表": "Adaptive list", "紧凑列表": "Compact list", "仅显示矛盾倍率": "Only show conflicting ratios", "矛盾": "Conflict", "确认冲突项修改": "Confirm conflict item modification", "该模型存在固定价格与倍率计费方式冲突，请确认选择": "The model has a fixed price and ratio billing method conflict, please confirm the selection", "当前计费": "Current billing", "修改为": "Modify to", "状态筛选": "Status filter", "没有模型可以复制": "No models to copy", "模型列表已复制到剪贴板": "Model list copied to clipboard", "复制失败": "Co<PERSON> failed", "复制已选": "<PERSON><PERSON> selected", "选择成功": "Selection successful", "暂无成功模型": "No successful models", "请先选择模型！": "Please select a model first!", "已复制 ${count} 个模型": "Copied ${count} models", "复制失败，请手动复制": "Copy failed, please copy manually", "过期时间快捷设置": "Expiration time quick settings", "批量创建时会在名称后自动添加随机后缀": "When creating in batches, a random suffix will be automatically added to the name", "额度必须大于0": "Quota must be greater than 0", "生成数量必须大于0": "Generation quantity must be greater than 0", "可用端点类型": "Supported endpoint types", "未登录，使用默认分组倍率：": "Not logged in, using default group ratio: ", "该服务器地址将影响支付回调地址以及默认首页展示的地址，请确保正确配置": "This server address will affect the payment callback address and the address displayed on the default homepage, please ensure correct configuration", "密钥聚合模式": "Key aggregation mode", "随机": "Random", "轮询": "Polling", "密钥文件 (.json)": "Key file (.json)", "点击上传文件或拖拽文件到这里": "Click to upload file or drag and drop file here", "仅支持 JSON 文件": "Only JSON files are supported", "仅支持 JSON 文件，支持多文件": "Only JSON files are supported, multiple files are supported", "请上传密钥文件": "Please upload the key file", "请填写部署地区": "Please fill in the deployment region", "请输入部署地区，例如：us-central1\n支持使用模型映射格式\n{\n    \"default\": \"us-central1\",\n    \"claude-3-5-sonnet-20240620\": \"europe-west1\"\n}": "Please enter the deployment region, for example: us-central1\nSupports using model mapping format\n{\n    \"default\": \"us-central1\",\n    \"claude-3-5-sonnet-20240620\": \"europe-west1\"\n}", "其他": "Other", "未知渠道": "Unknown channel", "切换为单密钥模式": "Switch to single key mode", "将仅保留第一个密钥文件，其余文件将被移除，是否继续？": "Only the first key file will be retained, and the remaining files will be removed. Continue?", "自定义模型名称": "Custom model name", "启用全部密钥": "Enable all keys", "以充值价格显示": "Show with recharge price", "美元汇率（非充值汇率，仅用于定价页面换算）": "USD exchange rate (not recharge rate, only used for pricing page conversion)", "美元汇率": "USD exchange rate"}